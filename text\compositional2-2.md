 2.2 Principles of Compositional Analysis
 Any objective statistical methodology should give equivalent results, when applied
 to two datasets which only differ by irrelevant details. The following four sections
 present four manipulations that should be irrelevant for compositions and that
 thus generate four invariance principles, with far-ranging consequences for the
 mathematics underpinning compositional data analysis (<PERSON><PERSON><PERSON>, 1986).
 2.2.1 Scaling Invariance
 For the theoretical approach taken in this book, we will consider a vector as a
 composition whenever its components represent the relative weight or importance
 of a set of parts forming a whole. In this case, the size or total weight of that whole is
 irrelevant. One can then remove that apparent influence by forcing the data vectors
 to share the same total sum with the closure operation,
 C.x/ D x
 1t x :
 (2.1)
 The components xi of a closed composition x D .x1;x2;:::;xD/ are values in the
 interval .0; 1/ and will be referred to as portions throughout this book. The term
 proportions will be reserved for ratios and relative sizes of components.
2.2 Principles of Compositional Analysis
 21
 By applying the closure, we can compare data from samples of different sizes,
 e.g., the beans, corn, and wheat in our simple kitchen example (even though their
 compositions were respectively reported for 100g, 82g, and 250mL of product);
 a sand/silt/clay composition of two sediment samples of 100g and 500g; and
 the proportion of votes to three parties in two electoral districts of 106 and
 107 inhabitants. For instance, the vectors a D Œ12;3;4, b D Œ2400;600;800,
 c D Œ12=17;3=17;4=17, andd D Œ12=13;3=13;4=13 represent all the same
 composition, as the relative importance (the ratios) between their components is the
 same. A sensible compositional analysis should therefore provide the same answer
 independently of the value of or even independently of whether the closure was
 applied or else the data vectors sum up to different values. We say the analysis will
 be then scaling invariant. Aitchison (1986)alreadyshowedthatall scaling-invariant
 functions of a composition can be expressed as functions of log ratios ln.xi=xj/.
 2.2.2 Perturbation Invariance
 Compositional data can be presented in many different units, and even when given
 in portions, it is still relevant in which physical quantities the components were
 originally measured: g, tons, mass%, cm3, vol.%, mols, molalities, ppm, ppb,
 partial pressure, energy,electric loading, money,time, persons,events, cells, mineral
 grains, etc. Even if it is clearly defined what substance we are measuring, how it
 is quantified is still a choice of the experimenter. It is extremely rare to find a
 composition where only one single type of quantification is meaningful: in most of
 the cases, several units could be equally chosen. And since different componentsare
 typically also qualitatively different, they might have differentinteresting properties.
 For instance, to change a mass proportion to a volume percentage, we need the
 densities of all components, which will be typically different. The same applies to
 changingprices between differentvaluations (from market price to book value, or to
 buying price, etc.), passing nutrition masses to energy intake, or cell type to energy
 consumption or biomass production. Thus, different analysts would end up having
 different values representing exactly the same compositions just due to a different
 choice of units.
 It is evident that the statistical analyses we can apply should thus give the same
 qualitative results regardless of the units chosen, as long as they contain the same
 information, i.e., as long as we can transform the units into each other, e.g., by
 rescaling each component by its density. This operation is done by a perturbation
 with a composition containing as entries the conversion factors (e.g., the densities)
 for each component. We would thus require a meaningful compositional analysis to
 give the same results (obviously, up to a change of units) when applied to a dataset
 perturbed by this vector. But since we never know which type of quantifications
 might else be considered, we should request invariance of the analysis with respect
 to perturbation with all possible weighting factors.
22 2 FundamentalConceptsofCompositionalDataAnalysis
 Thisprinciplehas thesamelevelas theprincipleof translationinvariancefor
 realdata.There,wewouldexpectthat,e.g.,whenwerepresentelectric(orgravity)
 potentialswithrespect toadifferent referencelevel,wewouldstillget thesame
 results(uptothatchoiceofreferencelevel).
 Thisprincipleisparticularlycriticalwhendealingwithdatasetsofmixedunits.
 Oftendifferent componentsaremeasured indifferent units. For instance, trace
 elementsareoftenmeasuredinppm,whileothersaregiveninmg,orevenkg,and
 fluidsmightbequantifiedinvolumeproportionsorfugacities.Unfortunately,any
 methodnothonoringperturbationinvariancewouldgivecompletelyarbitraryresults
 whentheunitsdonotmatch.Ontheotherhand,whenwedemandperturbation
 invariance,allsuchdatacouldbemeaningfullyanalyzedinacommonframework,
 aslongasthereexistsaperturbationbringingthemintothesamesystemofunits,
 evenifwedonotknowit.
 2.2.3 SubcompositionalCoherence
 Sometimes,onlyasubsetof theinitialpartsisuseful foraparticularapplication,
 andoneworkswith thecorrespondingsubcomposition, by reclosing thevector
 of the chosen components. Subcompositions play the same rolewith respect
 tocompositionsasmarginalsdo inconventional realmultivariateanalysis: they
 representsubspacesoflowerdimensionwheredatacanbeprojectedforinspection.
 Thishas several implications jointly referred toas subcompositional coherence
 Aitchison(1986):threeexamplesoftheseimplicationsfollow.
 First, ifwemeasurethedistancebetweentwoD-partcompositions, thismust
 behigherwhenmeasuredwithallDcomponents thanwhenmeasured inany
 subcomposition.
 Second, thetotaldispersionofaD-partcompositionaldatasetmustbehigher
 thanthedispersioninanysubcomposition.
 Finally, ifwefittedameaningfulmodel toaD-part compositional dataset,
 theresult shouldnotchangeifwe includeanewnon-informative(e.g., random)
 componentandworkwiththeresulting.DC1/-partcomposition.
 Remark2.1(Incoherenceofclassicalcovariance). Theclassicaldefinitions
 ofcovarianceandcorrelationcoefficientarenotsubcompositionallycoherent.
 Thisisconnectedtotwoproblems:thespuriouscorrelationandthenegative
 bias, first identifiedbyPearson (1897) and later rediscoveredbyChayes
 (1960).Thespuriouscorrelationproblemsstatesthatthecorrelationbetween
 ratioswithcommondenominators(e.g.,twocomponentsofaclosedcompo
sition)isarbitrarytoanuncertainextent.Thenegativebiasproblemappears
 becauseeachroworcolumnofthecovariancematrixofaclosedcomposition
 sumsuptozero:giventhatthevariancesarealwayspositive,thisimpliesthat
2.3 ElementaryCompositionalGraphics 23
 somecovariancesareforcedtowardsnegativevalues,notduetoanyincom
patibilityprocessbutbecauseoftheclosureitself.Forinstance,ifwecompute
 thecorrelationcoefficientbetweenMnOandMgOcontentinacompositional
 datasetofglacialsedimentgeochemistry,4wemayobtain0.95ifweusethe
 whole10-partcompositionor 0:726ifweuseonlythesubcompositionof
 elementsnot relatedtofeldspar(P–Mn–Mg–Fe–Ti).Giventhefundamental
 roleofcovarianceinstatistics,itisnotasurprisethatthereexistsafullseries
 of papersondetecting, cataloging, and trying tocircumvent the spurious
 correlation.AnaccountcanbefoundinAitchisonandEgozcue(2005).
 2.2.4 PermutationInvariance
 Lastbutnot least, resultsofanyanalysisshouldnotdependonthesequence in
 whichthecomponentsaregiveninthedataset.Thismightseemself-evident,butitis
 surprisinghowmanymethodshappentoviolateit.Forinstance,oneoftheapparent
 bypassesof theconstantsumofcovariancematrices(Remark2.1)wastoremove
 thelastcomponentofthedataset: inthatway, thedatasetwasnotsummingupto
 aconstantanymore,andthecovariancematrixwasapparentlyfree.That“solution”
 wasobviouslynotpermutationinvariant(andevenmore, itwasnotasolution,as
 correlationshadexactlythesamevalues,beingthusequallyforcedtowardsnegative
 spuriouscorrelations).
 Forthelog-ratioapproach,itisalsoaveryimportantprinciple,when,e.g.,weask
 whichmethodscanbemeaningfullyappliedtocoordinatesofcompositionaldata.
 Forinstance,anaiveEuclideandistanceofalrtransformeddata5isnotpermutation
 invariantandshouldthusnotbeused,e.g.,forclusteranalysis.Wewouldotherwise
 risktohavedifferentclusteringsdependingonwhichwas thelastvariableinthe
 dataset.ThiswillbefurtherdiscussedinRemark2.2.
 2.3 ElementaryCompositionalGraphics
 Compositionaldatasetsaretypicallyrepresentedinfourdifferentways:asHarker
 diagrams(scatterplotsoftwocomponents),asternarydiagrams(closedscatterplots
 ofthreecomponents),asscatterplotsoflogratiosofseveralparts,orassequences
 ofbarplotsorpieplots.Byfar,themostcommonwaysarethefirsttwo.
 4Thisdatasetcanbefoundonhttp://www.stat.boogaart.de/compositionsRBook.
 5Thealr(additivelogratio)transformationwasthefundamentaltransformationinAitchison(1986)
 approach,anditisdiscussedindetailinSect.2.5.7.
24
 2 Fundamental Concepts of Compositional Data Analysis
 These compositional graphics will be illustrated with a dataset of geochemistry
 of glacial sediments (Tolosana-Delgado and von Eynatten, 2010). The dataset is
 available on the book home page6:
 > GeoChemSed=read.csv("geochemsed.csv",
 header=TRUE, skip=1)[,-c(3,14,31,32)]
 > names(GeoChemSed)
 [1] "Sample" "GS"
 [7] "MgO"
 [13] "Ba"
 [19] "Ni"
 [25] "Y"
 "CaO"
 "Co"
 "Pb"
 "Zn"
 "SiO2" "TiO2" "Al2O3" "MnO"
 "Na2O" "K2O"
 "Cr"
 "Rb"
 "Zr"
 "Cu"
 "Sc"
 "Nd"
 "P2O5" "Fe2O3t"
 "Ga"
 "Sr"
 "Nb"
 "V"
 2.3.1 Sense and Nonsense of Scatterplots of Components
 Harker diagram is the name given in geochemistry to a conventional scatterplot
 of two components, without any transformation applied to them. For this reason,
 they may highlight any additive relationship between the variables plotted: e.g.,
 in the case of plotting two chemical components of a dataset evolving in several
 stages, Harker diagrams visually represent mass balance computations between the
 several stages (Cortes, 2009). Unfortunately, these diagrams are neither scaling nor
 perturbationinvariantand notsubcompositionallycoherent(Aitchison and Egozcue,
 2005): there is no guarantee that the plot of a closed subcomposition exhibits similar
 or even compatible patterns with the plot of the original dataset, even if the parts not
 included in the subcomposition are irrelevant for the process being studied. This is
 actually the spurious correlationproblem;thus, a regressionline drawn in such a plot
 cannot be trusted, in general terms. Figure 2.1 shows two Harker diagrams of the
 same components in the same dataset, exhibiting the subcompositional incoherence
 of this representation. One can obtain a Harker diagram by using the standard
 function plot(x,y).
 > par(mfrow=c(1,2), mar=c(4,4,0.5,0.5))
 > plot( clo(GeoChemSed[,3:12])[,c(4,5)])
 > plot( clo(GeoChemSed[,c(4,6,7,11,12)])[,c(2,3)])
 2.3.2 Ternary Diagrams
 Ternary diagrams are similar to scatterplots but display a closed three-part sub
compositions. If we would plot the three components of a closed composition in
 a three-dimensional scatterplot, all points would lie in a planar triangle spanned
 6http://www.stat.boogaart.de/compositionsRBook.
2.3 Elementary Compositional Graphics
 25
 0.06
 0.05
 0.04
 MgO
 0.03
 0.02
 0.01
 0.00
 0.35
 0.30
 0.25
 MgO
 0.0005 0.0010 0.0015 0.0020
 MnO
 0.20
 0.15
 0.012 0.014 0.016 0.018 0.020
 MnO
 Fig. 2.1 Examples of Harker diagrams on the sediment geochemistry dataset. (Left) Using the full
 dataset of major oxides. (Right) Using the subcomposition P–Mn–Mg–Fe–Ti
 Na2O
 viewing direction
 Na2O
 MgO
 MgO
 CaO
 Fig. 2.2 Example of a ternary diagram embedded in its original three-dimensional space
 by the three points .1;0;0/, .0;1;0/,and.0;0;1/as corners. The ternary diagram
 displays this triangle in the drawing plane with the corners annotated by the axis
 on which this corner lies. This is possible since closed datasets of three parts have
 only 2 degrees of freedom. Figure 2.2 illustrates this with the MgO,CaO, and Na2O
 subcomposition of the example dataset.
 For the interpretation of ternary diagrams, we can make use of the property
 that the orthogonal segments joining a point with the three sides of an equilateral
 triangle (the heights of that point) have always the same total length: the length of
 each segment is taken as the proportion of a given part. Ternary diagrams have the
 merit of actually representing the data as what they are: compositional and relative.
 Geoscientists are particularly used to this kind of representation. Figures 2.3 and 2.4
 show several ways in which the relative portions and proportionsof the components
 of a three-part composition can be read from a ternary diagram.
 It is worth mentioning that when the three parts represented have too different
 magnitudes, data tend to collapse on a border or a vertex, obscuring the structure:
 CaO
26
 2 Fundamental Concepts of Compositional Data Analysis
 C
 C
 (A+B+C)=1 0.9 0.8 0.7 0.6 0.5 0.4 0.3 0.2 0.1 0
 A B
 B
 (A+B+C)=0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1
 C
 1
 0.9
 0.8
 0.7
 0.5
 0.3
 0.2
 0.1
 0
 A B
 Fig. 2.3 In a ternary diagram, the portion of each part is represented by the portion of the height
 of the point over the side of the triangle opposite to the part on the total height of the triangle.
 However, it is difficult to judge height visually. If we assume the three sides of the triangle to
 form a sequence of axes, we can make a projection of the point onto the axis pointing towards the
 interesting part parallel to the previous axis. Consequently, a composition on an edge of the triangle
 only contains the two parts connected by the side, and a composition in a vertex of the triangle will
 only contain the part the vertex is linked to. Thus, all compositions along a line parallel to an axis
 have the same portion of the part opposite to the axis
 1
 0.9
 0.8
 0.7
 0.6
 0.4
 0.2
 0.1
 a typical practice here is to multiply each part by a constant and reclose the result.
 One can obtain a ternary diagram in R by using plot(x), with x an object of any
 compositional class (acomp or rcomp, see next section for details).
 > xc = acomp(GeoChemSed, c("MgO","CaO","Na2O"))
 > plot(xc)
 1
 0.9
 0.8
 0.7
 0.6
 0.5
 0.4
 0.3
 A
 (A+B+C)=1 0.9 0.8 0.7 0.6 0.5 0.4 0.3 0.2 0.1 0
 0
 0.1
2.3 Elementary Compositional Graphics
 C
 27
 B
 (A+B) 
B
 A
 =0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1
 (A+B) = 0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1
 A
 (A+B) = 1 0.9 0.8 0.7 0.6 0.5 0.4 0.3 0.2 0.1 0
 B
 (B+C) = 0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1
 C
 (B+C) = 1 0.9 0.8 0.7 0.6 0.5 0.4 0.3 0.2 0.1 0
 C
 A
 B
 B
 B
 (A+B) = 0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1
 A
 (A+B) = 1 0.9 0.8 0.7 0.6 0.5 0.4 0.3 0.2 0.1 0
 Fig. 2.4 The twopart subcompositions can be read in two ways: (1) by projecting the composition
 to an edge of the triangle along a ray coming from the vertex of the part that should be ignored.
 Thus, all compositions along such rays have the same subcomposition, when the part where the
 ray is coming from is removed. (2) By drawing a scale between 0 and 1 on a segment parallel to
 the axis opposite to the part we want to remove, passing through our datum
 2.3.3 Log-Ratio Scatterplots
 Log-ratio scatterplots are just scatterplots of the log ratio of two parts against
 that of two other parts, though sometimes the denominators are the same. This
 representation is fully coherent with the description of compositions given in
 Sect. 2.1 but on the other side, does not capture any mass balance between the
 represented parts. In fact, it is “independent” of such mass balances: whenever we
 C
 (A+C) =0 0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1
 A
 (A+C) =1 0.9 0.8 0.7 0.6 0.5 0.4 0.3 0.2 0.1 0
28
 2 Fundamental Concepts of Compositional Data Analysis
 −1.5
 −2.0
 log(TiO2/K2O)
 −2.5
 −3.0
 −3.5
 2.0
 2.2
 2.4
 2.6
 log(CaO/P2O5)
 2.8
 3.0
 Fig. 2.5 Example of a log-ratio scatter plot obtained using the formula interface. This example
 was obtained with plot(log(TiO2/K2O) log(CaO/P2O5),GeoChemSed). Recall that the for
mula syntax is y x
 cannot ensure that the studied system is not shrinking or growing, then log ratios of
 scatterplots display almost all the information we really have. For instance, if we are
 working with transported sediments, we do not knowif any part can be dissolved by
 water and removedfromthesystem; orwhendealingwithpartyvotesharesbetween
 two different elections, we do not know if exactly the same people took part in the
 election. In fact, most of the datasets obtained in natural systems (meaning, not in
 the lab under strictly controlled situations) will be in such situations. Despite these
 issues, scatterplots of log ratios are seldom encountered in applications. To generate
 a log-ratio scatterplot, one can use the formulainterface to plot(formula, data),
 providing the data set where the variables involved will be extracted, and the
 desired formula describing the log ratios:
 > plot(log(var1/var2)~log(var3/var4), dataset)
 > plot(I(var1/var2)~I(var3/var4), dataset, log="xy")
 Figure 2.5 shows an example obtained with the first syntax. These two syntax
 examples would only differ in the way axes are labeled, with the transformed values
 in the first case, with the original values of the ratios in the second case (using the
 commandIis neededto tell R that the quotient inside it are not symbolic formulae,
 but actual ratios).
 2.3.4 Bar Plots and Pie Charts
 A bar plot is a classical representation where all parts may be simultaneously
 represented. In a bar plot, one represents the amount of each part in an individual as
 a bar within a set. Ideally, the bars are stacked, with heights adding to the total of
 the composition (1 or 100%). Then, each individual is represented as one of such
2.4 Multivariate Scales
 wheat
 29
 1.0
 0.8
 0.6
 0.4
 0.2
 0.0
 soy
 wheat
 beans
 Other
 Protein
 Carbonates
 Fat
 Carbonates
 Fat
 Other
 Protein
 Fig. 2.6 Examples of bar plot (left) and pie plot (right). Note the presence of an extra segment on
 top of the bars for beans, showing the presence of a missing value
 stacked bars, possibly ordered if that is possible (e.g., in time series). Stacked bars
 are provided in R by the commandbarplot(x),whenx isa compositionaldataset.
 Individual compositions can also be displayed in the form of pie charts. Pie charts
 are produced by the pie(x) command, but now x must be a single composition
 (as only one pie diagram will be generated). Pie charts are not recommended
 for compositions of more than two parts, because the human eye is weak in the
 comparison of angles if they are not aligned (Bertin, 1967). Figure 2.6 is created by
 > barplot( acomp(Amounts),xlim=c(0,11) )
 > pie( acomp(Amounts["wheat",]), main="wheat")