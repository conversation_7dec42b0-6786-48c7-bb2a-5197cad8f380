<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Highly Variable Genes (HVG) in Scanpy Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
            color: white;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #ff6b6b;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }
        
        .method-box {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #4ecdc4;
        }
        
        .batch-box {
            background: linear-gradient(135deg, #ffd3a5 0%, #fd9853 100%);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #fd9853;
        }
        
        .formula-box {
            background: #f1f2f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            line-height: 1.4;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-counter {
            background: #ff6b6b;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        
        .navigation ul {
            list-style: none;
        }
        
        .navigation li {
            margin: 5px 0;
        }
        
        .navigation a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .navigation a:hover {
            color: #ff6b6b;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 107, 107, 0.4);
        }
        
        table {
            border-collapse: collapse;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .gene-expression {
            transition: all 0.3s ease;
        }
        
        .gene-expression:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="navigation">
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#methods">HVG Methods</a></li>
            <li><a href="#batch-processing">Batch Processing</a></li>
            <li><a href="#seurat-v3">Seurat v3</a></li>
            <li><a href="#implementation">Implementation</a></li>
            <li><a href="#examples">Examples</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧬 Highly Variable Genes (HVG)</h1>
            <p>Understanding HVG Methods and Batch Processing in Scanpy</p>
        </div>

        <section id="introduction" class="section">
            <h2>🔬 Introduction to Highly Variable Genes</h2>
            
            <div class="highlight-box">
                <strong>What are Highly Variable Genes (HVGs)?</strong><br>
                HVGs are genes that show high variability in expression across cells, often representing biologically meaningful differences between cell types, states, or conditions. Identifying HVGs is crucial for downstream analysis like clustering and trajectory inference.
            </div>

            <h3>Why Identify HVGs?</h3>
            <ul style="margin: 20px 0; padding-left: 30px;">
                <li><strong>Noise Reduction:</strong> Focus on genes with meaningful biological variation</li>
                <li><strong>Computational Efficiency:</strong> Reduce dimensionality for faster analysis</li>
                <li><strong>Biological Relevance:</strong> Capture genes driving cell-to-cell differences</li>
                <li><strong>Batch Effect Mitigation:</strong> Select genes consistently variable across batches</li>
            </ul>

            <div class="visualization">
                <h4>Gene Expression Variability Concept</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Low variability gene -->
                    <g transform="translate(150, 150)">
                        <circle cx="-80" cy="-50" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="-60" cy="-45" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="-40" cy="-55" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="-20" cy="-48" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="0" cy="-52" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="20" cy="-47" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="40" cy="-53" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="60" cy="-49" r="8" fill="#95a5a6" class="gene-expression"/>
                        <circle cx="80" cy="-51" r="8" fill="#95a5a6" class="gene-expression"/>
                        
                        <text x="0" y="30" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Low Variability Gene</text>
                        <text x="0" y="50" font-size="12" fill="#7f8c8d" text-anchor="middle">Similar expression across cells</text>
                    </g>
                    
                    <!-- High variability gene -->
                    <g transform="translate(450, 150)">
                        <circle cx="-80" cy="-80" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="-60" cy="20" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="-40" cy="-60" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="-20" cy="40" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="0" cy="-20" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="20" cy="60" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="40" cy="-40" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="60" cy="10" r="8" fill="#e74c3c" class="gene-expression"/>
                        <circle cx="80" cy="-70" r="8" fill="#e74c3c" class="gene-expression"/>
                        
                        <text x="0" y="100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">High Variability Gene</text>
                        <text x="0" y="120" font-size="12" fill="#7f8c8d" text-anchor="middle">Variable expression across cells</text>
                    </g>
                    
                    <!-- Axes -->
                    <line x1="50" y1="250" x2="550" y2="250" stroke="#bdc3c7" stroke-width="2"/>
                    <text x="300" y="280" font-size="12" fill="#7f8c8d" text-anchor="middle">Cells</text>
                    
                    <line x1="50" y1="50" x2="50" y2="250" stroke="#bdc3c7" stroke-width="2"/>
                    <text x="25" y="150" font-size="12" fill="#7f8c8d" text-anchor="middle" transform="rotate(-90 25 150)">Expression</text>
                </svg>
            </div>
        </section>

        <section id="methods" class="section">
            <h2>⚙️ HVG Detection Methods</h2>

            <p>Scanpy implements several methods for identifying highly variable genes, each with different assumptions and use cases:</p>

            <div class="method-box">
                <h3>🔹 Method 1: Seurat (flavor='seurat')</h3>
                <p><strong>Based on:</strong> Dispersion-to-mean ratio normalization</p>

                <div class="formula-box">
                    $$\text{Dispersion} = \frac{\text{Variance}}{\text{Mean}}$$
                    $$\text{Normalized Dispersion} = \frac{\text{Dispersion} - \text{Expected Dispersion}}{\text{Std of Dispersion}}$$
                </div>

                <p><strong>Key Features:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Expects log-transformed data</li>
                    <li>Bins genes by mean expression</li>
                    <li>Normalizes dispersion within each bin</li>
                    <li>Good for general-purpose HVG detection</li>
                </ul>
            </div>

            <div class="method-box">
                <h3>🔹 Method 2: Cell Ranger (flavor='cell_ranger')</h3>
                <p><strong>Based on:</strong> Median absolute deviation (MAD) normalization</p>

                <div class="formula-box">
                    $$\text{Normalized Dispersion} = \frac{\text{Dispersion} - \text{Median Dispersion}}{\text{MAD of Dispersion}}$$
                </div>

                <p><strong>Key Features:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Uses percentile-based binning</li>
                    <li>More robust to outliers (uses median/MAD)</li>
                    <li>Developed by 10X Genomics</li>
                    <li>Good for droplet-based data</li>
                </ul>
            </div>

            <div class="method-box">
                <h3>🔹 Method 3: Seurat v3 (flavor='seurat_v3')</h3>
                <p><strong>Based on:</strong> Variance stabilizing transformation with LOESS regression</p>

                <div class="formula-box">
                    $$\text{log}_{10}(\text{Variance}) = \text{LOESS}(\text{log}_{10}(\text{Mean}))$$
                    $$\text{Normalized Variance} = \frac{1}{(N-1) \cdot \sigma_{reg}^2} \left[ N \cdot \mu^2 + \sum x_{clipped}^2 - 2\mu \sum x_{clipped} \right]$$
                </div>

                <p><strong>Key Features:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Expects raw count data (not log-transformed)</li>
                    <li>Uses LOESS regression for variance modeling</li>
                    <li>Clips extreme values to reduce outlier effects</li>
                    <li>Most sophisticated method, best for complex datasets</li>
                </ul>
            </div>

            <div class="visualization">
                <h4>Method Comparison Workflow</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Seurat workflow -->
                    <g transform="translate(120, 80)">
                        <rect x="-80" y="-30" width="160" height="40" fill="#3498db" rx="5"/>
                        <text x="0" y="-5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">Seurat Method</text>

                        <rect x="-80" y="20" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="40" font-size="10" fill="#2c3e50" text-anchor="middle">Log-transformed data</text>

                        <rect x="-80" y="60" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="80" font-size="10" fill="#2c3e50" text-anchor="middle">Dispersion = Var/Mean</text>

                        <rect x="-80" y="100" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="120" font-size="10" fill="#2c3e50" text-anchor="middle">Bin by mean expression</text>

                        <rect x="-80" y="140" width="160" height="30" fill="#27ae60" rx="3"/>
                        <text x="0" y="160" font-size="10" fill="white" text-anchor="middle">Z-score normalization</text>
                    </g>

                    <!-- Cell Ranger workflow -->
                    <g transform="translate(350, 80)">
                        <rect x="-80" y="-30" width="160" height="40" fill="#e74c3c" rx="5"/>
                        <text x="0" y="-5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">Cell Ranger Method</text>

                        <rect x="-80" y="20" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="40" font-size="10" fill="#2c3e50" text-anchor="middle">Log-transformed data</text>

                        <rect x="-80" y="60" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="80" font-size="10" fill="#2c3e50" text-anchor="middle">Dispersion = Var/Mean</text>

                        <rect x="-80" y="100" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="120" font-size="10" fill="#2c3e50" text-anchor="middle">Percentile-based bins</text>

                        <rect x="-80" y="140" width="160" height="30" fill="#27ae60" rx="3"/>
                        <text x="0" y="160" font-size="10" fill="white" text-anchor="middle">MAD normalization</text>
                    </g>

                    <!-- Seurat v3 workflow -->
                    <g transform="translate(580, 80)">
                        <rect x="-80" y="-30" width="160" height="40" fill="#9b59b6" rx="5"/>
                        <text x="0" y="-5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">Seurat v3 Method</text>

                        <rect x="-80" y="20" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="40" font-size="10" fill="#2c3e50" text-anchor="middle">Raw count data</text>

                        <rect x="-80" y="60" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="80" font-size="10" fill="#2c3e50" text-anchor="middle">LOESS regression</text>

                        <rect x="-80" y="100" width="160" height="30" fill="#ecf0f1" rx="3"/>
                        <text x="0" y="120" font-size="10" fill="#2c3e50" text-anchor="middle">Value clipping</text>

                        <rect x="-80" y="140" width="160" height="30" fill="#27ae60" rx="3"/>
                        <text x="0" y="160" font-size="10" fill="white" text-anchor="middle">Normalized variance</text>
                    </g>

                    <!-- Arrows pointing to final selection -->
                    <path d="M 120 200 L 350 250" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#methodArrow)"/>
                    <path d="M 350 200 L 350 250" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#methodArrow)"/>
                    <path d="M 580 200 L 350 250" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#methodArrow)"/>

                    <!-- Final selection -->
                    <rect x="270" y="260" width="160" height="40" fill="#f39c12" rx="5"/>
                    <text x="350" y="285" font-size="14" fill="white" text-anchor="middle" font-weight="bold">HVG Selection</text>

                    <defs>
                        <marker id="methodArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Method Selection Guidelines</h3>

            <table>
                <tr>
                    <th>Method</th>
                    <th>Data Type</th>
                    <th>Best For</th>
                    <th>Batch Support</th>
                </tr>
                <tr>
                    <td><strong>Seurat</strong></td>
                    <td>Log-transformed</td>
                    <td>General purpose, well-established</td>
                    <td>✅ Yes</td>
                </tr>
                <tr>
                    <td><strong>Cell Ranger</strong></td>
                    <td>Log-transformed</td>
                    <td>Droplet data, robust to outliers</td>
                    <td>✅ Yes</td>
                </tr>
                <tr>
                    <td><strong>Seurat v3</strong></td>
                    <td>Raw counts</td>
                    <td>Complex datasets, batch integration</td>
                    <td>✅ Advanced</td>
                </tr>
            </table>
        </section>

        <section id="batch-processing" class="section">
            <h2>🔄 Batch Processing in HVG Detection</h2>

            <div class="highlight-box">
                <strong>Why Batch Processing?</strong><br>
                When analyzing multiple datasets or experimental batches, batch effects can confound HVG detection. Batch processing ensures that genes are consistently variable across different experimental conditions, reducing batch-specific artifacts.
            </div>

            <h3>Batch Processing Workflow</h3>

            <div class="batch-box">
                <span class="step-counter">1</span>
                <strong>Separate Analysis per Batch</strong>
                <p>HVG detection is performed independently within each batch using the selected method (Seurat, Cell Ranger, or Seurat v3).</p>

                <div class="code-block">
# For each batch b in unique(batch_info):
for b in np.unique(batch_info):
    data_batch = data[batch_info == b]
    # Perform HVG detection on data_batch
    hvg_batch = detect_hvg_single_batch(data_batch)
                </div>
            </div>

            <div class="batch-box">
                <span class="step-counter">2</span>
                <strong>Gene Filtering per Batch</strong>
                <p>Genes with zero variance or insufficient expression are filtered out within each batch to avoid numerical issues.</p>

                <div class="code-block">
# Filter genes with min_cells=1 per batch
filt, _ = filter_genes(data_batch, min_cells=1, inplace=False)
adata_subset = adata_subset[:, filt]
                </div>
            </div>

            <div class="batch-box">
                <span class="step-counter">3</span>
                <strong>Aggregation Across Batches</strong>
                <p>Results from individual batches are combined using different strategies depending on the method:</p>

                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Mean aggregation:</strong> means, dispersions, dispersions_norm</li>
                    <li><strong>Sum aggregation:</strong> highly_variable (count of batches)</li>
                    <li><strong>Intersection:</strong> genes variable in ALL batches</li>
                </ul>
            </div>

            <div class="visualization">
                <h4>Batch Processing Visualization</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Batch 1 -->
                    <g transform="translate(120, 80)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="10"/>
                        <text x="0" y="-10" font-size="14" fill="white" text-anchor="middle" font-weight="bold">Batch 1</text>
                        <text x="0" y="10" font-size="12" fill="white" text-anchor="middle">1000 cells</text>
                        <text x="0" y="25" font-size="12" fill="white" text-anchor="middle">500 HVGs</text>

                        <!-- Sample genes -->
                        <circle cx="-30" cy="-20" r="3" fill="#e74c3c"/>
                        <circle cx="-10" cy="-25" r="3" fill="#27ae60"/>
                        <circle cx="10" cy="-15" r="3" fill="#f39c12"/>
                        <circle cx="30" cy="-22" r="3" fill="#9b59b6"/>
                        <text x="0" y="55" font-size="10" fill="#2c3e50" text-anchor="middle">Gene A, B, C, D</text>
                    </g>

                    <!-- Batch 2 -->
                    <g transform="translate(350, 80)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="10"/>
                        <text x="0" y="-10" font-size="14" fill="white" text-anchor="middle" font-weight="bold">Batch 2</text>
                        <text x="0" y="10" font-size="12" fill="white" text-anchor="middle">800 cells</text>
                        <text x="0" y="25" font-size="12" fill="white" text-anchor="middle">450 HVGs</text>

                        <!-- Sample genes -->
                        <circle cx="-30" cy="-20" r="3" fill="#e74c3c"/>
                        <circle cx="-10" cy="-25" r="3" fill="#95a5a6"/>
                        <circle cx="10" cy="-15" r="3" fill="#f39c12"/>
                        <circle cx="30" cy="-22" r="3" fill="#9b59b6"/>
                        <text x="0" y="55" font-size="10" fill="#2c3e50" text-anchor="middle">Gene A, E, C, D</text>
                    </g>

                    <!-- Batch 3 -->
                    <g transform="translate(580, 80)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#27ae60" stroke="#2c3e50" stroke-width="2" rx="10"/>
                        <text x="0" y="-10" font-size="14" fill="white" text-anchor="middle" font-weight="bold">Batch 3</text>
                        <text x="0" y="10" font-size="12" fill="white" text-anchor="middle">1200 cells</text>
                        <text x="0" y="25" font-size="12" fill="white" text-anchor="middle">600 HVGs</text>

                        <!-- Sample genes -->
                        <circle cx="-30" cy="-20" r="3" fill="#e74c3c"/>
                        <circle cx="-10" cy="-25" r="3" fill="#27ae60"/>
                        <circle cx="10" cy="-15" r="3" fill="#95a5a6"/>
                        <circle cx="30" cy="-22" r="3" fill="#9b59b6"/>
                        <text x="0" y="55" font-size="10" fill="#2c3e50" text-anchor="middle">Gene A, B, F, D</text>
                    </g>

                    <!-- Arrows to aggregation -->
                    <path d="M 120 140 L 350 200" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#batchArrow)"/>
                    <path d="M 350 140 L 350 200" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#batchArrow)"/>
                    <path d="M 580 140 L 350 200" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#batchArrow)"/>

                    <!-- Aggregation results -->
                    <g transform="translate(350, 250)">
                        <rect x="-120" y="-40" width="240" height="100" fill="#f39c12" stroke="#2c3e50" stroke-width="2" rx="10"/>
                        <text x="0" y="-15" font-size="16" fill="white" text-anchor="middle" font-weight="bold">Batch Aggregation</text>

                        <!-- Gene statistics -->
                        <text x="-80" y="5" font-size="11" fill="white" text-anchor="start">Gene A: 3/3 batches</text>
                        <text x="-80" y="20" font-size="11" fill="white" text-anchor="start">Gene B: 2/3 batches</text>
                        <text x="-80" y="35" font-size="11" fill="white" text-anchor="start">Gene C: 2/3 batches</text>

                        <text x="20" y="5" font-size="11" fill="white" text-anchor="start">Gene D: 3/3 batches</text>
                        <text x="20" y="20" font-size="11" fill="white" text-anchor="start">Gene E: 1/3 batches</text>
                        <text x="20" y="35" font-size="11" fill="white" text-anchor="start">Gene F: 1/3 batches</text>
                    </g>

                    <!-- Final selection -->
                    <path d="M 350 310 L 350 350" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#batchArrow)"/>

                    <rect x="270" y="360" width="160" height="30" fill="#2c3e50" rx="5"/>
                    <text x="350" y="380" font-size="12" fill="white" text-anchor="middle" font-weight="bold">Final HVG Set</text>

                    <defs>
                        <marker id="batchArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Batch Aggregation Strategies</h3>

            <div class="batch-box">
                <h4>📊 For Dispersion-based Methods (Seurat, Cell Ranger)</h4>
                <div class="code-block">
# Aggregate statistics across batches
df = df.groupby("gene", observed=True).agg(
    dict(
        means="mean",                    # Average mean expression
        dispersions="mean",              # Average dispersion
        dispersions_norm="mean",         # Average normalized dispersion
        highly_variable="sum",           # Count of batches where HVG
    )
)

# Sort by batch count and normalized dispersion
df.sort_values(
    ["highly_variable_nbatches", "dispersions_norm"],
    ascending=False,
    inplace=True,
)
                </div>
            </div>

            <div class="batch-box">
                <h4>🧬 For Seurat v3 Method</h4>
                <div class="code-block">
# Calculate normalized variance per batch
for b in np.unique(batch_info):
    data_batch = data[batch_info == b]
    # ... LOESS regression and variance calculation ...
    norm_gene_vars.append(norm_gene_var.reshape(1, -1))

# Aggregate across batches
norm_gene_vars = np.concatenate(norm_gene_vars, axis=0)
ranked_norm_gene_vars = np.argsort(np.argsort(-norm_gene_vars, axis=1), axis=1)

# Count batches where gene is in top N
num_batches_high_var = np.sum(
    (ranked_norm_gene_vars < n_top_genes).astype(int), axis=0
)

# Calculate median rank across batches
median_ranked = np.ma.median(ma_ranked, axis=0).filled(np.nan)
                </div>
            </div>

            <h3>Key Batch Processing Outputs</h3>

            <table>
                <tr>
                    <th>Output</th>
                    <th>Description</th>
                    <th>Calculation</th>
                </tr>
                <tr>
                    <td><code>highly_variable_nbatches</code></td>
                    <td>Number of batches where gene is HVG</td>
                    <td>Sum across batches</td>
                </tr>
                <tr>
                    <td><code>highly_variable_intersection</code></td>
                    <td>Gene is HVG in ALL batches</td>
                    <td>nbatches == total_batches</td>
                </tr>
                <tr>
                    <td><code>highly_variable_rank</code></td>
                    <td>Median rank across batches (Seurat v3)</td>
                    <td>Median of within-batch ranks</td>
                </tr>
                <tr>
                    <td><code>variances_norm</code></td>
                    <td>Average normalized variance (Seurat v3)</td>
                    <td>Mean across batches</td>
                </tr>
            </table>
        </section>

        <section id="seurat-v3" class="section">
            <h2>🔬 Deep Dive: Seurat v3 Method</h2>

            <div class="highlight-box">
                <strong>Seurat v3 Innovation:</strong><br>
                The Seurat v3 method represents a significant advancement in HVG detection, using variance stabilizing transformation with LOESS regression to model the mean-variance relationship more accurately than previous methods.
            </div>

            <h3>Step-by-Step Seurat v3 Algorithm</h3>

            <div class="method-box">
                <span class="step-counter">1</span>
                <strong>Data Preparation</strong>
                <p>Unlike other methods, Seurat v3 expects raw count data (not log-transformed):</p>

                <div class="code-block">
# Check for raw count data
if check_values and not check_nonnegative_integers(data):
    warnings.warn(
        f"`{flavor=!r}` expects raw count data, but non-integers were found.",
        UserWarning,
    )
                </div>
            </div>

            <div class="method-box">
                <span class="step-counter">2</span>
                <strong>LOESS Regression Modeling</strong>
                <p>For each batch, fit a LOESS model to predict log-variance from log-mean:</p>

                <div class="formula-box">
                    $$\log_{10}(\text{variance}) = \text{LOESS}(\log_{10}(\text{mean}))$$
                </div>

                <div class="code-block">
# Fit LOESS model for mean-variance relationship
y = np.log10(var[not_const])  # log variance
x = np.log10(mean[not_const])  # log mean
model = loess(x, y, span=span, degree=2)
model.fit()
estimat_var[not_const] = model.outputs.fitted_values
reg_std = np.sqrt(10**estimat_var)  # Regularized standard deviation
                </div>
            </div>

            <div class="method-box">
                <span class="step-counter">3</span>
                <strong>Value Clipping</strong>
                <p>Clip extreme values to reduce the impact of outliers:</p>

                <div class="formula-box">
                    $$\text{clip\_val} = \sigma_{reg} \cdot \sqrt{N} + \mu$$
                    $$x_{clipped} = \min(x, \text{clip\_val})$$
                </div>

                <div class="code-block">
# Clip large values as in Seurat
N = data_batch.shape[0]
vmax = np.sqrt(N)
clip_val = reg_std * vmax + mean

# Apply clipping
np.putmask(
    batch_counts,
    batch_counts > clip_val_broad,
    clip_val_broad,
)
                </div>
            </div>

            <div class="method-box">
                <span class="step-counter">4</span>
                <strong>Normalized Variance Calculation</strong>
                <p>Calculate the normalized variance using the clipped data:</p>

                <div class="formula-box">
                    $$\text{norm\_var} = \frac{1}{(N-1) \cdot \sigma_{reg}^2} \left[ N \cdot \mu^2 + \sum x_{clipped}^2 - 2\mu \sum x_{clipped} \right]$$
                </div>

                <div class="code-block">
norm_gene_var = (1 / ((N - 1) * np.square(reg_std))) * (
    (N * np.square(mean))
    + squared_batch_counts_sum
    - 2 * batch_counts_sum * mean
)
                </div>
            </div>

            <div class="visualization">
                <h4>Seurat v3 LOESS Regression Visualization</h4>
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Axes -->
                    <line x1="80" y1="320" x2="520" y2="320" stroke="#2c3e50" stroke-width="2"/>
                    <line x1="80" y1="80" x2="80" y2="320" stroke="#2c3e50" stroke-width="2"/>

                    <!-- Axis labels -->
                    <text x="300" y="350" font-size="14" fill="#2c3e50" text-anchor="middle">log₁₀(Mean Expression)</text>
                    <text x="40" y="200" font-size="14" fill="#2c3e50" text-anchor="middle" transform="rotate(-90 40 200)">log₁₀(Variance)</text>

                    <!-- Data points -->
                    <circle cx="120" cy="280" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="140" cy="260" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="160" cy="240" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="180" cy="220" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="200" cy="200" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="220" cy="180" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="240" cy="160" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="260" cy="140" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="280" cy="120" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="300" cy="110" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="320" cy="105" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="340" cy="100" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="360" cy="98" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="380" cy="96" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="400" cy="95" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="420" cy="94" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="440" cy="93" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="460" cy="92" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="480" cy="91" r="3" fill="#3498db" opacity="0.6"/>
                    <circle cx="500" cy="90" r="3" fill="#3498db" opacity="0.6"/>

                    <!-- LOESS curve -->
                    <path d="M 120 275 Q 200 195 300 115 Q 400 98 500 92" stroke="#e74c3c" stroke-width="3" fill="none"/>

                    <!-- Residuals (some examples) -->
                    <line x1="160" y1="240" x2="160" y2="220" stroke="#27ae60" stroke-width="2"/>
                    <line x1="220" y1="180" x2="220" y2="165" stroke="#27ae60" stroke-width="2"/>
                    <line x1="340" y1="100" x2="340" y2="108" stroke="#27ae60" stroke-width="2"/>

                    <!-- Legend -->
                    <circle cx="150" cy="50" r="3" fill="#3498db"/>
                    <text x="160" y="55" font-size="12" fill="#2c3e50">Gene expression data</text>

                    <line x1="280" y1="47" x2="300" y2="47" stroke="#e74c3c" stroke-width="3"/>
                    <text x="310" y="52" font-size="12" fill="#2c3e50">LOESS regression fit</text>

                    <line x1="450" y1="47" x2="470" y2="47" stroke="#27ae60" stroke-width="2"/>
                    <text x="480" y="52" font-size="12" fill="#2c3e50">Residuals</text>

                    <text x="300" y="25" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                        LOESS Regression: Mean-Variance Relationship
                    </text>
                </svg>
            </div>

            <h3>Batch Processing in Seurat v3</h3>

            <div class="batch-box">
                <h4>🔄 Per-Batch Processing</h4>
                <p>Each batch is processed independently to calculate normalized variance:</p>

                <div class="code-block">
norm_gene_vars = []
for b in np.unique(batch_info):
    data_batch = data[batch_info == b]

    # Calculate mean and variance for this batch
    mean, var = _get_mean_var(data_batch)

    # Fit LOESS model
    model = loess(x, y, span=span, degree=2)
    model.fit()

    # Calculate normalized variance
    norm_gene_var = calculate_normalized_variance(...)
    norm_gene_vars.append(norm_gene_var.reshape(1, -1))

# Concatenate results from all batches
norm_gene_vars = np.concatenate(norm_gene_vars, axis=0)
                </div>
            </div>

            <div class="batch-box">
                <h4>📊 Ranking and Selection</h4>
                <p>Genes are ranked within each batch, then aggregated across batches:</p>

                <div class="code-block">
# Rank genes within each batch (lower rank = more variable)
ranked_norm_gene_vars = np.argsort(np.argsort(-norm_gene_vars, axis=1), axis=1)

# Count how many batches each gene is in top N
num_batches_high_var = np.sum(
    (ranked_norm_gene_vars < n_top_genes).astype(int), axis=0
)

# Calculate median rank across batches
ranked_norm_gene_vars[ranked_norm_gene_vars >= n_top_genes] = np.nan
ma_ranked = np.ma.masked_invalid(ranked_norm_gene_vars)
median_ranked = np.ma.median(ma_ranked, axis=0).filled(np.nan)
                </div>
            </div>

            <h3>Seurat v3 vs Seurat v3 Paper</h3>

            <table>
                <tr>
                    <th>Aspect</th>
                    <th>seurat_v3</th>
                    <th>seurat_v3_paper</th>
                </tr>
                <tr>
                    <td><strong>Primary Sort</strong></td>
                    <td>Median rank</td>
                    <td>Number of batches</td>
                </tr>
                <tr>
                    <td><strong>Tie Breaker</strong></td>
                    <td>Number of batches</td>
                    <td>Median rank</td>
                </tr>
                <tr>
                    <td><strong>Use Case</strong></td>
                    <td>General HVG detection</td>
                    <td>Integration features</td>
                </tr>
                <tr>
                    <td><strong>Seurat Equivalent</strong></td>
                    <td>FindVariableFeatures</td>
                    <td>SelectIntegrationFeatures</td>
                </tr>
            </table>
        </section>

        <section id="implementation" class="section">
            <h2>💻 Implementation Details</h2>

            <h3>Core Function Signature</h3>

            <div class="code-block">
def highly_variable_genes(
    adata: AnnData,
    *,
    layer: str | None = None,
    n_top_genes: int | None = None,
    min_disp: float = 0.5,
    max_disp: float = np.inf,
    min_mean: float = 0.0125,
    max_mean: float = 3,
    span: float = 0.3,
    n_bins: int = 20,
    flavor: HVGFlavor = "seurat",
    subset: bool = False,
    inplace: bool = True,
    batch_key: str | None = None,
    check_values: bool = True,
) -> pd.DataFrame | None
            </div>

            <h3>Key Implementation Functions</h3>

            <div class="method-box">
                <h4>🔹 Single Batch Processing</h4>
                <div class="code-block">
def _highly_variable_genes_single_batch(
    adata: AnnData,
    *,
    layer: str | None = None,
    cutoff: _Cutoffs | int,
    n_bins: int = 20,
    flavor: Literal["seurat", "cell_ranger"] = "seurat",
) -> pd.DataFrame:
    """
    Process HVG detection for a single batch
    Returns DataFrame with highly_variable, means, dispersions, dispersions_norm
    """
    X = _get_obs_rep(adata, layer=layer)

    # Calculate mean and variance
    mean, var = materialize_as_ndarray(_get_mean_var(X))

    # Calculate dispersion
    dispersion = var / mean

    # Normalize dispersion within bins
    df["dispersions_norm"] = (df["dispersions"] - disp_stats["avg"]) / disp_stats["dev"]

    return df
                </div>
            </div>

            <div class="method-box">
                <h4>🔹 Batch Processing</h4>
                <div class="code-block">
def _highly_variable_genes_batched(
    adata: AnnData,
    batch_key: str,
    *,
    layer: str | None,
    n_bins: int,
    flavor: Literal["seurat", "cell_ranger"],
    cutoff: _Cutoffs | int,
) -> pd.DataFrame:
    """
    Process HVG detection across multiple batches
    """
    batches = adata.obs[batch_key].cat.categories
    dfs = []

    for batch in batches:
        adata_subset = adata[adata.obs[batch_key] == batch]

        # Filter genes with minimal expression
        filt, _ = filter_genes(data_subset, min_cells=1, inplace=False)
        adata_subset = adata_subset[:, filt]

        # Process single batch
        hvg = _highly_variable_genes_single_batch(adata_subset, ...)
        dfs.append(hvg)

    # Aggregate results
    df = pd.concat(dfs, axis=0)
    df = df.groupby("gene").agg({
        "means": "mean",
        "dispersions": "mean",
        "dispersions_norm": "mean",
        "highly_variable": "sum",
    })

    return df
                </div>
            </div>

            <h3>Performance Optimizations</h3>

            <div class="batch-box">
                <h4>⚡ Numba Acceleration</h4>
                <p>Critical computations use Numba for speed:</p>

                <div class="code-block">
@numba.njit(cache=True, parallel=False)
def _sum_and_sum_squares_clipped(
    indices: NDArray[np.integer],
    data: NDArray[np.floating],
    *,
    n_cols: int,
    clip_val: NDArray[np.float64],
    nnz: int,
) -> tuple[NDArray[np.float64], NDArray[np.float64]]:
    """
    Efficiently compute clipped sums for sparse matrices
    Used in Seurat v3 method for value clipping
    """
    squared_batch_counts_sum = np.zeros(n_cols, dtype=np.float64)
    batch_counts_sum = np.zeros(n_cols, dtype=np.float64)

    for i in numba.prange(nnz):
        idx = indices[i]
        element = min(np.float64(data[i]), clip_val[idx])
        squared_batch_counts_sum[idx] += element**2
        batch_counts_sum[idx] += element

    return squared_batch_counts_sum, batch_counts_sum
                </div>
            </div>

            <div class="batch-box">
                <h4>🗂️ Sparse Matrix Support</h4>
                <p>Efficient handling of sparse single-cell data:</p>

                <div class="code-block">
# Handle both dense and sparse matrices
if isinstance(data_batch, CSBase):
    if isinstance(data_batch, CSRBase):
        batch_counts = data_batch
    else:
        batch_counts = sparse.csr_matrix(data_batch)

    # Use optimized sparse operations
    squared_batch_counts_sum, batch_counts_sum = _sum_and_sum_squares_clipped(
        batch_counts.indices,
        batch_counts.data,
        n_cols=batch_counts.shape[1],
        clip_val=clip_val,
        nnz=batch_counts.nnz,
    )
else:
    # Dense matrix operations
    batch_counts = data_batch.astype(np.float64).copy()
    # ... dense operations ...
                </div>
            </div>
        </section>

        <section id="examples" class="section">
            <h2>🧪 Practical Examples</h2>

            <h3>Basic HVG Detection</h3>

            <div class="code-block">
import scanpy as sc
import pandas as pd

# Load example data
adata = sc.datasets.pbmc3k_processed()

# Method 1: Seurat method (default)
sc.pp.highly_variable_genes(adata, flavor='seurat', n_top_genes=2000)

# Method 2: Cell Ranger method
sc.pp.highly_variable_genes(adata, flavor='cell_ranger', n_top_genes=2000)

# Method 3: Seurat v3 method (requires raw counts)
adata_raw = sc.datasets.pbmc3k()  # Raw counts
sc.pp.highly_variable_genes(adata_raw, flavor='seurat_v3', n_top_genes=2000)

# View results
print(adata.var[['highly_variable', 'means', 'dispersions', 'dispersions_norm']].head())
            </div>

            <h3>Batch Processing Example</h3>

            <div class="code-block">
# Simulate batch information
import numpy as np
np.random.seed(42)
n_obs = adata.n_obs
batch_labels = np.random.choice(['Batch1', 'Batch2', 'Batch3'], size=n_obs)
adata.obs['batch'] = pd.Categorical(batch_labels)

# HVG detection with batch processing
sc.pp.highly_variable_genes(
    adata,
    flavor='seurat',
    n_top_genes=2000,
    batch_key='batch',  # Key parameter for batch processing
    inplace=True
)

# Examine batch-specific results
print("Batch-specific HVG statistics:")
print(f"Total HVGs: {adata.var['highly_variable'].sum()}")
print(f"HVGs in all batches: {adata.var['highly_variable_intersection'].sum()}")
print(f"Average batches per HVG: {adata.var['highly_variable_nbatches'].mean():.2f}")

# Plot batch distribution
import matplotlib.pyplot as plt
plt.figure(figsize=(10, 6))
plt.hist(adata.var['highly_variable_nbatches'], bins=4, alpha=0.7)
plt.xlabel('Number of batches where gene is HVG')
plt.ylabel('Number of genes')
plt.title('Distribution of HVG batch counts')
plt.show()
            </div>

            <h3>Advanced Seurat v3 Example</h3>

            <div class="code-block">
# Seurat v3 with batch processing
sc.pp.highly_variable_genes(
    adata_raw,  # Raw count data required
    flavor='seurat_v3',
    n_top_genes=2000,
    batch_key='batch',
    span=0.3,  # LOESS span parameter
    check_values=True,  # Verify raw counts
    inplace=True
)

# Compare seurat_v3 vs seurat_v3_paper
sc.pp.highly_variable_genes(
    adata_raw.copy(),
    flavor='seurat_v3_paper',  # Different sorting strategy
    n_top_genes=2000,
    batch_key='batch',
    inplace=True
)

# Examine ranking differences
print("Seurat v3 method results:")
print(adata_raw.var[['highly_variable_rank', 'highly_variable_nbatches', 'variances_norm']].head(10))
            </div>

            <h3>Visualization Examples</h3>

            <div class="code-block">
# Plot HVG results
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

# Plot 1: Mean vs Dispersion
sc.pl.highly_variable_genes(adata, ax=axes[0])
axes[0].set_title('Seurat Method')

# Plot 2: Batch comparison
if 'highly_variable_nbatches' in adata.var.columns:
    axes[1].scatter(
        adata.var['means'],
        adata.var['dispersions_norm'],
        c=adata.var['highly_variable_nbatches'],
        cmap='viridis',
        alpha=0.6
    )
    axes[1].set_xlabel('Mean expression')
    axes[1].set_ylabel('Normalized dispersion')
    axes[1].set_title('HVG Batch Distribution')

# Plot 3: Top HVGs
top_hvgs = adata.var.nlargest(20, 'dispersions_norm').index
axes[2].barh(range(20), adata.var.loc[top_hvgs, 'dispersions_norm'])
axes[2].set_yticks(range(20))
axes[2].set_yticklabels(top_hvgs)
axes[2].set_xlabel('Normalized dispersion')
axes[2].set_title('Top 20 HVGs')

plt.tight_layout()
plt.show()
            </div>

            <button class="interactive-button" onclick="showAdvancedTips()">Show Advanced Tips →</button>

            <div id="advanced-tips" style="display: none;">
                <h3>🎯 Advanced Tips and Best Practices</h3>

                <div class="highlight-box">
                    <h4>Method Selection Guidelines:</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Seurat:</strong> Good default choice, well-established</li>
                        <li><strong>Cell Ranger:</strong> Better for droplet data with many zeros</li>
                        <li><strong>Seurat v3:</strong> Best for complex datasets with batches</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4>Parameter Tuning:</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>n_top_genes:</strong> 2000-4000 for most analyses</li>
                        <li><strong>span:</strong> 0.3 (default) works well, increase for smoother fit</li>
                        <li><strong>n_bins:</strong> 20 (default), increase for more genes</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4>Batch Processing Tips:</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Use <code>highly_variable_intersection</code> for conservative selection</li>
                        <li>Consider <code>highly_variable_nbatches >= 2</code> for moderate selection</li>
                        <li>Check batch balance before HVG detection</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Add smooth scrolling for navigation
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Interactive functionality
        function showAdvancedTips() {
            document.getElementById('advanced-tips').style.display = 'block';
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([document.getElementById('advanced-tips')]).catch((err) => console.log(err.message));
            }
        }

        // Add interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to gene expression circles
            const geneExpressions = document.querySelectorAll('.gene-expression');
            geneExpressions.forEach(circle => {
                circle.addEventListener('mouseenter', function() {
                    this.style.filter = 'brightness(1.3)';
                    this.style.cursor = 'pointer';
                });

                circle.addEventListener('mouseleave', function() {
                    this.style.filter = 'brightness(1)';
                });
            });

            // Add hover effects to table cells
            const tableCells = document.querySelectorAll('td, th');
            tableCells.forEach(cell => {
                cell.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.2s';
                    this.style.backgroundColor = '#e8f5e8';
                });

                cell.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.backgroundColor = '';
                });
            });

            // Add progress indicator for navigation
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');

            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });

            // Add animation to step counters
            const stepCounters = document.querySelectorAll('.step-counter');
            stepCounters.forEach(counter => {
                counter.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(360deg)';
                    this.style.transition = 'transform 0.5s';
                });

                counter.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });

            // Add interactive code block functionality
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.addEventListener('click', function() {
                    // Select all text in the code block
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Show notification
                    showNotification('Code copied to clipboard!');
                });
            });

            // Add method box interactions
            const methodBoxes = document.querySelectorAll('.method-box, .batch-box');
            methodBoxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.transition = 'transform 0.3s ease';
                    this.style.boxShadow = '0 4px 12px rgba(255, 107, 107, 0.2)';
                });

                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });
        });

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                max-width: 80%;
                text-align: center;
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS for active navigation and enhanced interactivity
        const style = document.createElement('style');
        style.textContent = `
            .navigation a.active {
                color: #ff6b6b !important;
                font-weight: bold;
            }

            .navigation a.active::before {
                content: "→ ";
                color: #ff6b6b;
            }

            .formula-box:hover {
                box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block:hover {
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block::after {
                content: "Click to select all";
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.1);
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
                white-space: nowrap;
            }

            .code-block:hover::after {
                opacity: 1;
            }

            .code-block pre {
                white-space: pre-wrap;
                word-wrap: break-word;
                margin: 0;
                font-family: inherit;
            }

            .visualization:hover {
                transform: translateY(-2px);
                transition: transform 0.3s ease;
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
            }

            .highlight-box:hover {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
