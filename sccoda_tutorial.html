<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>scCODA: Bayesian Compositional Single-Cell Data Analysis Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
            color: white;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #ff6b6b;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }
        
        .model-box {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #4ecdc4;
        }
        
        .bayesian-box {
            background: linear-gradient(135deg, #dda0dd 0%, #ba55d3 100%);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #9370db;
            color: white;
        }
        
        .formula-box {
            background: #f1f2f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            line-height: 1.4;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-counter {
            background: #ff6b6b;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }
        
        .navigation ul {
            list-style: none;
        }
        
        .navigation li {
            margin: 5px 0;
        }
        
        .navigation a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .navigation a:hover {
            color: #ff6b6b;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 107, 107, 0.4);
        }
        
        table {
            border-collapse: collapse;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            width: 100%;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .problem-highlight {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .solution-highlight {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4ecdc4;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#problem">The Problem</a></li>
            <li><a href="#model">scCODA Model</a></li>
            <li><a href="#bayesian">Bayesian Framework</a></li>
            <li><a href="#inference">Parameter Inference</a></li>
            <li><a href="#applications">Applications</a></li>
            <li><a href="#performance">Performance</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧬 scCODA</h1>
            <p>Bayesian Model for Compositional Single-Cell Data Analysis</p>
        </div>

        <section id="introduction" class="section">
            <h2>🔬 Introduction to scCODA</h2>
            
            <div class="highlight-box">
                <strong>What is scCODA?</strong><br>
                scCODA (single-cell Compositional Data Analysis) is a Bayesian framework designed to detect statistically credible changes in cell-type compositions from single-cell RNA-sequencing data, while properly accounting for the compositional nature of the data and controlling false discovery rates.
            </div>

            <h3>Key Innovation</h3>
            <p>Traditional methods for analyzing cell-type composition changes often ignore the <strong>compositional nature</strong> of single-cell data, leading to false discoveries due to negative correlations inherent in proportional data.</p>

            <div class="visualization">
                <h4>Single-Cell Compositional Analysis Challenge</h4>
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Control sample -->
                    <g transform="translate(150, 150)">
                        <text x="0" y="-100" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Control Sample</text>
                        
                        <!-- Cell type pie chart representation -->
                        <circle cx="0" cy="0" r="60" fill="#3498db" opacity="0.8"/>
                        <path d="M 0 -60 A 60 60 0 0 1 52 -30 L 0 0 Z" fill="#e74c3c" opacity="0.8"/>
                        <path d="M 52 -30 A 60 60 0 0 1 30 52 L 0 0 Z" fill="#27ae60" opacity="0.8"/>
                        <path d="M 30 52 A 60 60 0 0 1 -52 30 L 0 0 Z" fill="#f39c12" opacity="0.8"/>
                        <path d="M -52 30 A 60 60 0 0 1 0 -60 L 0 0 Z" fill="#9b59b6" opacity="0.8"/>
                        
                        <!-- Labels -->
                        <text x="0" y="90" font-size="12" fill="#2c3e50" text-anchor="middle">T cells: 40%</text>
                        <text x="0" y="105" font-size="12" fill="#2c3e50" text-anchor="middle">B cells: 20%</text>
                        <text x="0" y="120" font-size="12" fill="#2c3e50" text-anchor="middle">NK cells: 15%</text>
                        <text x="0" y="135" font-size="12" fill="#2c3e50" text-anchor="middle">Monocytes: 25%</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 250 150 L 350 150" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <text x="300" y="140" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">Disease</text>
                    
                    <!-- Disease sample -->
                    <g transform="translate(550, 150)">
                        <text x="0" y="-100" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Disease Sample</text>
                        
                        <!-- Modified pie chart -->
                        <circle cx="0" cy="0" r="60" fill="#3498db" opacity="0.8"/>
                        <path d="M 0 -60 A 60 60 0 0 1 42 -42 L 0 0 Z" fill="#e74c3c" opacity="0.4"/>
                        <path d="M 42 -42 A 60 60 0 0 1 42 42 L 0 0 Z" fill="#27ae60" opacity="0.8"/>
                        <path d="M 42 42 A 60 60 0 0 1 -42 42 L 0 0 Z" fill="#f39c12" opacity="0.8"/>
                        <path d="M -42 42 A 60 60 0 0 1 0 -60 L 0 0 Z" fill="#9b59b6" opacity="0.8"/>
                        
                        <!-- Labels with changes -->
                        <text x="0" y="90" font-size="12" fill="#2c3e50" text-anchor="middle">T cells: 45% ↑</text>
                        <text x="0" y="105" font-size="12" fill="#e74c3c" text-anchor="middle">B cells: 10% ↓</text>
                        <text x="0" y="120" font-size="12" fill="#2c3e50" text-anchor="middle">NK cells: 20% ↑</text>
                        <text x="0" y="135" font-size="12" fill="#2c3e50" text-anchor="middle">Monocytes: 25% →</text>
                    </g>
                    
                    <!-- Problem annotation -->
                    <text x="350" y="250" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">
                        Problem: Are T cells and NK cells truly increased?
                    </text>
                    <text x="350" y="270" font-size="12" fill="#7f8c8d" text-anchor="middle">
                        Or is this just a compositional effect from B cell decrease?
                    </text>
                    
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Why scCODA Matters</h3>
            <ul style="margin: 20px 0; padding-left: 30px;">
                <li><strong>Compositional Awareness:</strong> Properly handles the constraint that cell-type proportions sum to 1</li>
                <li><strong>Low Sample Size:</strong> Works effectively with few experimental replicates</li>
                <li><strong>False Discovery Control:</strong> Automatically controls false discovery rate (FDR)</li>
                <li><strong>Reference Selection:</strong> Automatically selects appropriate reference cell types</li>
                <li><strong>Bayesian Framework:</strong> Provides uncertainty quantification and credible intervals</li>
            </ul>
        </section>

        <section id="problem" class="section">
            <h2>⚠️ The Compositional Data Problem</h2>

            <div class="problem-highlight">
                <strong>Core Issue:</strong> Single-cell data is inherently compositional - cell-type counts are proportional in nature due to the fixed total number of cells per sample. This creates negative correlations between cell types that can lead to false discoveries.
            </div>

            <h3>Why Standard Methods Fail</h3>

            <div class="model-box">
                <h4>🔹 Negative Correlation Bias</h4>
                <p>When one cell type decreases, others appear to increase relatively, even if their absolute numbers remain constant.</p>

                <div class="formula-box">
                    $$\text{If } \sum_{k=1}^{K} p_k = 1 \text{, then } \text{Cor}(p_i, p_j) < 0 \text{ for } i \neq j$$
                </div>
            </div>

            <div class="model-box">
                <h4>🔹 Low Sample Size Challenge</h4>
                <p>Single-cell experiments often have few biological replicates, making standard statistical tests unreliable.</p>

                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Insufficient degrees of freedom for frequentist tests</li>
                    <li>High variability between samples</li>
                    <li>Need for robust uncertainty quantification</li>
                </ul>
            </div>

            <div class="visualization">
                <h4>Compositional Bias Illustration</h4>
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Before perturbation -->
                    <g transform="translate(150, 100)">
                        <text x="0" y="-20" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Before Perturbation</text>

                        <!-- Bar chart representation -->
                        <rect x="-80" y="0" width="40" height="80" fill="#3498db" opacity="0.8"/>
                        <rect x="-30" y="0" width="40" height="60" fill="#e74c3c" opacity="0.8"/>
                        <rect x="20" y="0" width="40" height="40" fill="#27ae60" opacity="0.8"/>
                        <rect x="70" y="0" width="40" height="20" fill="#f39c12" opacity="0.8"/>

                        <!-- Labels -->
                        <text x="-60" y="95" font-size="10" fill="#2c3e50" text-anchor="middle">A: 40%</text>
                        <text x="-10" y="95" font-size="10" fill="#2c3e50" text-anchor="middle">B: 30%</text>
                        <text x="40" y="95" font-size="10" fill="#2c3e50" text-anchor="middle">C: 20%</text>
                        <text x="90" y="95" font-size="10" fill="#2c3e50" text-anchor="middle">D: 10%</text>

                        <!-- Total -->
                        <text x="0" y="120" font-size="12" fill="#2c3e50" text-anchor="middle" font-weight="bold">Total: 100%</text>
                    </g>

                    <!-- Arrow -->
                    <path d="M 250 200 L 350 200" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#problemArrow)"/>
                    <text x="300" y="190" font-size="12" fill="#e74c3c" text-anchor="middle" font-weight="bold">B cells depleted</text>

                    <!-- After perturbation -->
                    <g transform="translate(450, 100)">
                        <text x="0" y="-20" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">After Perturbation</text>

                        <!-- Modified bar chart -->
                        <rect x="-80" y="0" width="40" height="80" fill="#3498db" opacity="0.8"/>
                        <rect x="-30" y="0" width="40" height="20" fill="#e74c3c" opacity="0.4"/>
                        <rect x="20" y="0" width="40" height="40" fill="#27ae60" opacity="0.8"/>
                        <rect x="70" y="0" width="40" height="20" fill="#f39c12" opacity="0.8"/>

                        <!-- Labels with apparent changes -->
                        <text x="-60" y="95" font-size="10" fill="#27ae60" text-anchor="middle">A: 50% ↑</text>
                        <text x="-10" y="95" font-size="10" fill="#e74c3c" text-anchor="middle">B: 10% ↓</text>
                        <text x="40" y="95" font-size="10" fill="#27ae60" text-anchor="middle">C: 25% ↑</text>
                        <text x="90" y="95" font-size="10" fill="#27ae60" text-anchor="middle">D: 15% ↑</text>

                        <!-- Total -->
                        <text x="0" y="120" font-size="12" fill="#2c3e50" text-anchor="middle" font-weight="bold">Total: 100%</text>
                    </g>

                    <!-- Problem explanation -->
                    <rect x="50" y="250" width="500" height="120" fill="#fff2f2" stroke="#e74c3c" stroke-width="2" rx="10"/>
                    <text x="300" y="275" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">
                        False Discovery Problem
                    </text>
                    <text x="300" y="295" font-size="12" fill="#2c3e50" text-anchor="middle">
                        Standard tests would report A, C, and D as "significantly increased"
                    </text>
                    <text x="300" y="315" font-size="12" fill="#2c3e50" text-anchor="middle">
                        But only B cells actually changed - others just appear increased
                    </text>
                    <text x="300" y="335" font-size="12" fill="#2c3e50" text-anchor="middle">
                        due to the compositional constraint (proportions must sum to 1)
                    </text>
                    <text x="300" y="355" font-size="12" fill="#7f8c8d" text-anchor="middle" font-style="italic">
                        This leads to inflated false discovery rates!
                    </text>

                    <defs>
                        <marker id="problemArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Inadequate Current Approaches</h3>

            <table>
                <tr>
                    <th>Method</th>
                    <th>Approach</th>
                    <th>Problem</th>
                </tr>
                <tr>
                    <td><strong>Poisson Regression</strong></td>
                    <td>Univariate tests per cell type</td>
                    <td>Ignores compositional constraints</td>
                </tr>
                <tr>
                    <td><strong>Wilcoxon Rank-Sum</strong></td>
                    <td>Non-parametric comparison</td>
                    <td>No compositional adjustment</td>
                </tr>
                <tr>
                    <td><strong>t-tests</strong></td>
                    <td>Independent cell type testing</td>
                    <td>False positive inflation</td>
                </tr>
                <tr>
                    <td><strong>Beta-Binomial</strong></td>
                    <td>Overdispersion modeling</td>
                    <td>Still non-compositional</td>
                </tr>
            </table>

            <div class="solution-highlight">
                <strong>scCODA Solution:</strong> Joint modeling of all cell-type proportions using a Bayesian hierarchical framework that explicitly accounts for compositional constraints and provides automatic false discovery rate control.
            </div>
        </section>

        <section id="model" class="section">
            <h2>🏗️ The scCODA Model</h2>

            <div class="highlight-box">
                <strong>Model Overview:</strong> scCODA uses a hierarchical Dirichlet-Multinomial distribution with a logit-normal spike-and-slab prior to model cell-type counts while accounting for compositional constraints and enabling automatic variable selection.
            </div>

            <h3>Mathematical Foundation</h3>

            <div class="model-box">
                <span class="step-counter">1</span>
                <strong>Data Structure</strong>
                <p>We observe cell counts $Y_{N \times K}$ for $K$ cell types across $N$ samples with $M$ covariates.</p>

                <div class="formula-box">
                    $$Y \sim \text{DirMult}(\phi, y)$$
                    $$\log(\phi) = \alpha + X\beta$$
                </div>
            </div>

            <div class="model-box">
                <span class="step-counter">2</span>
                <strong>Hierarchical Structure</strong>
                <p>Effects are modeled hierarchically with covariate-specific scaling:</p>

                <div class="formula-box">
                    $$\alpha_k \sim \mathcal{N}(0, 5) \quad \forall k \in [1, \ldots, K]$$
                    $$\tilde{\beta} = \tau \odot \beta$$
                    $$\tau_{m,k} = \frac{\exp(t_{m,k})}{1 + \exp(t_{m,k})} \quad \forall m,k$$
                </div>
            </div>

            <div class="model-box">
                <span class="step-counter">3</span>
                <strong>Spike-and-Slab Prior</strong>
                <p>Automatic variable selection through logit-normal relaxation:</p>

                <div class="formula-box">
                    $$t_{m,k} \sim \mathcal{N}(0, 50) \quad \forall m,k$$
                    $$\tilde{\beta}_{m,k} = \sigma_m^2 \gamma_{m,k} \quad \forall m,k$$
                    $$\sigma_m^2 \sim \text{HalfCauchy}(1) \quad \forall m$$
                    $$\gamma_{m,k} \sim \mathcal{N}(0, 1) \quad \forall m,k$$
                </div>
            </div>

            <div class="visualization">
                <h4>scCODA Model Structure</h4>
                <svg width="700" height="500" viewBox="0 0 700 500">
                    <!-- Data layer -->
                    <g transform="translate(350, 80)">
                        <rect x="-60" y="-20" width="120" height="40" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="14" fill="white" text-anchor="middle" font-weight="bold">Y ~ DirMult(φ, y)</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Observed cell counts</text>
                    </g>

                    <!-- Linear predictor -->
                    <g transform="translate(350, 160)">
                        <rect x="-80" y="-20" width="160" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="14" fill="white" text-anchor="middle" font-weight="bold">log(φ) = α + Xβ</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Linear predictor</text>
                    </g>

                    <!-- Intercepts -->
                    <g transform="translate(150, 240)">
                        <rect x="-50" y="-20" width="100" height="40" fill="#27ae60" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">α ~ N(0, 5)</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Intercepts</text>
                    </g>

                    <!-- Effects -->
                    <g transform="translate(550, 240)">
                        <rect x="-50" y="-20" width="100" height="40" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">β̃ = τ ⊙ β</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Masked effects</text>
                    </g>

                    <!-- Spike-and-slab -->
                    <g transform="translate(400, 320)">
                        <rect x="-70" y="-20" width="140" height="40" fill="#f39c12" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">τ = sigmoid(t)</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Inclusion probability</text>
                    </g>

                    <!-- Hyperpriors -->
                    <g transform="translate(200, 400)">
                        <rect x="-60" y="-20" width="120" height="40" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">t ~ N(0, 50)</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Spike-slab prior</text>
                    </g>

                    <g transform="translate(500, 400)">
                        <rect x="-60" y="-20" width="120" height="40" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">σ² ~ HC(1)</text>
                        <text x="0" y="50" font-size="12" fill="#2c3e50" text-anchor="middle">Global scale</text>
                    </g>

                    <!-- Arrows -->
                    <path d="M 350 120 L 350 140" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#modelArrow)"/>
                    <path d="M 270 180 L 200 220" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#modelArrow)"/>
                    <path d="M 430 180 L 500 220" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#modelArrow)"/>
                    <path d="M 550 280 L 450 300" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#modelArrow)"/>
                    <path d="M 400 360 L 250 380" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#modelArrow)"/>
                    <path d="M 400 360 L 450 380" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#modelArrow)"/>

                    <defs>
                        <marker id="modelArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#2c3e50"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Key Model Components</h3>

            <div class="bayesian-box">
                <h4>🎯 Dirichlet-Multinomial Distribution</h4>
                <p>Accounts for overdispersion and uncertainty in cell-type proportions beyond what a simple multinomial would capture.</p>

                <div class="formula-box" style="background: rgba(255,255,255,0.9); color: #2c3e50;">
                    $$P(Y|\phi, y) = \frac{\Gamma(y+1)}{\prod_{k=1}^K \Gamma(Y_k+1)} \cdot \frac{B(Y + \phi)}{B(\phi)}$$
                </div>
            </div>

            <div class="bayesian-box">
                <h4>🎯 Logit-Normal Spike-and-Slab</h4>
                <p>Continuous relaxation of discrete variable selection, allowing smooth posterior inference while maintaining sparsity.</p>

                <div class="formula-box" style="background: rgba(255,255,255,0.9); color: #2c3e50;">
                    $$\tau_{m,k} = \text{sigmoid}(t_{m,k}) = \frac{1}{1 + \exp(-t_{m,k})}$$
                </div>
            </div>

            <div class="bayesian-box">
                <h4>🎯 Reference Cell Type</h4>
                <p>Compositional constraint requires fixing one cell type as reference ($\beta_{\text{ref}} = 0$). Can be automatically selected.</p>

                <div class="formula-box" style="background: rgba(255,255,255,0.9); color: #2c3e50;">
                    $$K_{\text{ref}} = \arg\min_{k} \text{Disp}(Y'_{:,k}) \text{ s.t. } \frac{|\{n: Y_{n,k} > 0\}|}{N} \geq t$$
                </div>
            </div>
        </section>

        <section id="bayesian" class="section">
            <h2>🎲 Bayesian Framework & Inference</h2>

            <div class="highlight-box">
                <strong>Bayesian Advantage:</strong> Unlike frequentist methods, Bayesian inference naturally handles uncertainty, works with small sample sizes, and provides automatic model selection through the spike-and-slab prior.
            </div>

            <h3>Parameter Inference via MCMC</h3>

            <div class="model-box">
                <span class="step-counter">1</span>
                <strong>Hamiltonian Monte Carlo (HMC)</strong>
                <p>scCODA uses HMC sampling for efficient posterior exploration:</p>

                <div class="code-block">
# HMC Configuration
n_iterations = 20000
burn_in = 5000
leapfrog_steps = 10
step_size = auto_adjusted

# Parameter initialization
α_k ~ N(0, 1)  # Random initialization
γ_m,k ~ N(0, 1)  # Random initialization
t_m,k = 0  # Unbiased model selection
σ² = 1  # Initial scale
                </div>
            </div>

            <div class="model-box">
                <span class="step-counter">2</span>
                <strong>Inclusion Probability Calculation</strong>
                <p>After MCMC sampling, calculate posterior inclusion probabilities:</p>

                <div class="formula-box">
                    $$P_{\text{inc}}(\beta_{m,k}) = \frac{1}{H} \sum_{h=1}^{H} \mathbb{I}(|\beta_{m,k}^{(h)}| \geq 10^{-3})$$
                </div>

                <p>Where $H$ is the number of MCMC iterations and $\mathbb{I}$ is the indicator function.</p>
            </div>

            <div class="visualization">
                <h4>MCMC Sampling Process</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Sampling trajectory -->
                    <g transform="translate(100, 200)">
                        <text x="0" y="-150" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                            MCMC Parameter Trajectory
                        </text>

                        <!-- Parameter space -->
                        <rect x="0" y="-100" width="500" height="200" fill="none" stroke="#bdc3c7" stroke-width="2"/>

                        <!-- Burn-in phase -->
                        <path d="M 20 0 Q 50 -50 80 -20 Q 110 10 140 -30 Q 170 -60 200 -10"
                              stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.7"/>
                        <text x="110" y="-80" font-size="12" fill="#e74c3c" text-anchor="middle">Burn-in (5000)</text>

                        <!-- Sampling phase -->
                        <path d="M 200 -10 Q 230 20 260 -5 Q 290 -30 320 5 Q 350 40 380 10 Q 410 -20 440 15 Q 470 45 480 20"
                              stroke="#27ae60" stroke-width="3" fill="none"/>
                        <text x="340" y="80" font-size="12" fill="#27ae60" text-anchor="middle">Sampling (15000)</text>

                        <!-- Axes -->
                        <line x1="0" y1="100" x2="500" y2="100" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-100" x2="0" y2="100" stroke="#34495e" stroke-width="2"/>

                        <text x="250" y="130" font-size="12" fill="#34495e" text-anchor="middle">Iteration</text>
                        <text x="-30" y="0" font-size="12" fill="#34495e" text-anchor="middle" transform="rotate(-90 -30 0)">β value</text>

                        <!-- Vertical line separating phases -->
                        <line x1="200" y1="-100" x2="200" y2="100" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
                    </g>
                </svg>
            </div>

            <h3>False Discovery Rate Control</h3>

            <div class="bayesian-box">
                <h4>🎯 Direct Posterior Probability Approach</h4>
                <p>scCODA uses a direct posterior probability method to control FDR automatically:</p>

                <div class="formula-box" style="background: rgba(255,255,255,0.9); color: #2c3e50;">
                    $$\widehat{\text{FDR}}(c) = \frac{\sum_{\beta_{m,k} \in J(c)} (1 - P(\beta_{m,k}))}{|J(c)|}$$
                </div>

                <p>Where $J(c) = \{\beta_{m,k} : 1 - P(\beta_{m,k}) \leq c\}$ is the set of credible effects for threshold $c$.</p>
            </div>

            <div class="model-box">
                <span class="step-counter">3</span>
                <strong>Optimal Threshold Selection</strong>
                <p>Find the threshold that maximizes discoveries while controlling FDR:</p>

                <div class="formula-box">
                    $$c^* = \min_{0 < c < 1, \widehat{\text{FDR}}(c) < \alpha} c$$
                </div>

                <p>For desired FDR level $\alpha$ (typically 0.05 or 0.2).</p>
            </div>

            <div class="visualization">
                <h4>FDR Control Mechanism</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- FDR curve -->
                    <g transform="translate(50, 250)">
                        <!-- Axes -->
                        <line x1="0" y1="0" x2="500" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="0" x2="0" y2="-200" stroke="#34495e" stroke-width="2"/>

                        <!-- FDR curve -->
                        <path d="M 0 -20 Q 100 -40 200 -80 Q 300 -120 400 -160 Q 450 -180 500 -190"
                              stroke="#e74c3c" stroke-width="3" fill="none"/>

                        <!-- FDR threshold line -->
                        <line x1="0" y1="-100" x2="500" y2="-100" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="510" y="-95" font-size="12" fill="#27ae60">FDR = 0.2</text>

                        <!-- Optimal threshold -->
                        <line x1="300" y1="0" x2="300" y2="-200" stroke="#9b59b6" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="305" y="-10" font-size="12" fill="#9b59b6">c*</text>

                        <!-- Labels -->
                        <text x="250" y="25" font-size="12" fill="#34495e" text-anchor="middle">Inclusion Probability Threshold</text>
                        <text x="-25" y="-100" font-size="12" fill="#34495e" text-anchor="middle" transform="rotate(-90 -25 -100)">FDR</text>

                        <!-- Legend -->
                        <line x1="350" y1="-180" x2="380" y2="-180" stroke="#e74c3c" stroke-width="3"/>
                        <text x="390" y="-175" font-size="12" fill="#2c3e50">Estimated FDR</text>

                        <line x1="350" y1="-160" x2="380" y2="-160" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="390" y="-155" font-size="12" fill="#2c3e50">Target FDR</text>
                    </g>
                </svg>
            </div>

            <h3>Credible Intervals</h3>

            <div class="model-box">
                <h4>🔹 High-Density Intervals (HDI)</h4>
                <p>For credible effects, calculate HDI excluding zero-inflated samples:</p>

                <div class="formula-box">
                    $$\widehat{\text{HDI}}(\beta_{m,k}) = \text{HDI}(\beta_{m,k} | \tau_{m,k} > 0)$$
                </div>

                <p>This provides uncertainty quantification for the magnitude of credible effects.</p>
            </div>

            <div class="code-block">
# Example: Processing MCMC results
def process_mcmc_results(mcmc_samples):
    # Calculate inclusion probabilities
    inclusion_probs = []
    for param in mcmc_samples:
        prob = np.mean(np.abs(param) >= 1e-3)
        inclusion_probs.append(prob)

    # Determine FDR threshold
    fdr_threshold = find_optimal_threshold(inclusion_probs, target_fdr=0.2)

    # Identify credible effects
    credible_effects = inclusion_probs >= fdr_threshold

    # Calculate HDI for credible effects
    hdis = []
    for i, param in enumerate(mcmc_samples):
        if credible_effects[i]:
            # Exclude zero samples for HDI calculation
            non_zero_samples = param[np.abs(param) >= 1e-3]
            hdi = calculate_hdi(non_zero_samples, credibility=0.95)
            hdis.append(hdi)

    return credible_effects, hdis
            </div>
        </section>

        <section id="applications" class="section">
            <h2>🧪 Real-World Applications</h2>

            <div class="highlight-box">
                <strong>Validated Results:</strong> scCODA has been successfully applied to multiple real datasets, identifying experimentally verified compositional changes that were missed by standard methods.
            </div>

            <h3>Key Application Examples</h3>

            <div class="model-box">
                <h4>🔹 Supercentenarian B Cell Depletion</h4>
                <p><strong>Dataset:</strong> PBMCs from supercentenarians (n=7) vs. younger controls (n=5)</p>
                <p><strong>Finding:</strong> scCODA correctly identified B cell decrease, validated by FACS measurements</p>
                <p><strong>Reference:</strong> CD16+ monocytes (automatically selected)</p>

                <div class="code-block">
# scCODA analysis
sccoda.tl.compositional_analysis(
    adata,
    formula="age_group",
    reference_cell_type="CD16+ monocytes",
    fdr_target=0.2
)

# Result: B cells credibly decreased (FDR < 0.2)
# Confirmed by independent FACS validation
                </div>
            </div>

            <div class="model-box">
                <h4>🔹 Alzheimer's Disease Microglia</h4>
                <p><strong>Dataset:</strong> Microglia from AD mouse model (n=2 per group)</p>
                <p><strong>Finding:</strong> Disease-associated microglia (DAM) increase in cortex, no change in cerebellum</p>
                <p><strong>Validation:</strong> Confirmed by immunostaining</p>

                <div class="visualization">
                    <svg width="500" height="200" viewBox="0 0 500 200">
                        <!-- Cortex results -->
                        <g transform="translate(125, 100)">
                            <rect x="-100" y="-60" width="200" height="120" fill="#fff2f2" stroke="#e74c3c" stroke-width="2" rx="10"/>
                            <text x="0" y="-35" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Cortex (AD affected)</text>
                            <text x="0" y="-15" font-size="12" fill="#27ae60" text-anchor="middle">✓ DAM increased</text>
                            <text x="0" y="0" font-size="12" fill="#27ae60" text-anchor="middle">✓ Microglia 2 changed</text>
                            <text x="0" y="15" font-size="12" fill="#7f8c8d" text-anchor="middle">Reference: Microglia 1</text>
                            <text x="0" y="35" font-size="10" fill="#e74c3c" text-anchor="middle">scCODA detected changes</text>
                        </g>

                        <!-- Cerebellum results -->
                        <g transform="translate(375, 100)">
                            <rect x="-100" y="-60" width="200" height="120" fill="#f2fff2" stroke="#27ae60" stroke-width="2" rx="10"/>
                            <text x="0" y="-35" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Cerebellum (Unaffected)</text>
                            <text x="0" y="-15" font-size="12" fill="#95a5a6" text-anchor="middle">○ No DAM change</text>
                            <text x="0" y="0" font-size="12" fill="#95a5a6" text-anchor="middle">○ No microglia change</text>
                            <text x="0" y="15" font-size="12" fill="#7f8c8d" text-anchor="middle">Known to be unperturbed</text>
                            <text x="0" y="35" font-size="10" fill="#27ae60" text-anchor="middle">scCODA: no false positives</text>
                        </g>
                    </svg>
                </div>
            </div>

            <div class="model-box">
                <h4>🔹 COVID-19 Immune Response</h4>
                <p><strong>Dataset:</strong> Bronchoalveolar lavage fluid (n=4 healthy, n=3 mild, n=6 severe)</p>
                <p><strong>Findings:</strong> T cell depletion, neutrophil increase with severity</p>
                <p><strong>Validation:</strong> Confirmed by larger independent studies</p>

                <table style="margin: 15px 0;">
                    <tr>
                        <th>Cell Type</th>
                        <th>scCODA Finding</th>
                        <th>Literature Validation</th>
                    </tr>
                    <tr>
                        <td>T cells</td>
                        <td>Decreased in severe</td>
                        <td>✓ Established risk factor</td>
                    </tr>
                    <tr>
                        <td>NK cells</td>
                        <td>Increased mild vs healthy</td>
                        <td>✓ Confirmed by FACS studies</td>
                    </tr>
                    <tr>
                        <td>Neutrophils</td>
                        <td>Increased in severe</td>
                        <td>✓ Associated with poor outcomes</td>
                    </tr>
                </table>
            </div>

            <h3>Practical Usage</h3>

            <div class="code-block">
import scanpy as sc
import sccoda
from sccoda import util

# Load and prepare data
adata = sc.read_h5ad("single_cell_data.h5ad")

# Set up compositional analysis
sccoda_data = util.cell_composition_data(
    adata,
    "cell_type",
    "sample_id",
    "condition"
)

# Run scCODA analysis
model = sccoda.model.CompositionalAnalysis(
    sccoda_data,
    formula="condition",
    reference_cell_type="auto"  # Automatic selection
)

# MCMC sampling
model.sample_hmc(
    num_results=20000,
    num_burnin_samples=5000
)

# Get results with FDR control
results = model.summary(fdr_target=0.2)
print(results)

# Visualize results
sccoda.pl.boxplots(
    sccoda_data,
    feature_name="condition",
    plot_facets=True
)

sccoda.pl.effects_barplot(
    model,
    parameter="condition[T.disease]",
    plot_credible_intervals=True
)
            </div>
        </section>

        <section id="performance" class="section">
            <h2>📊 Performance & Benchmarking</h2>

            <div class="highlight-box">
                <strong>Superior Performance:</strong> Comprehensive benchmarks show scCODA significantly outperforms existing methods, especially in low-sample regimes typical of single-cell experiments.
            </div>

            <h3>Benchmark Results</h3>

            <div class="visualization">
                <h4>Method Comparison (Matthews Correlation Coefficient)</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Performance bars -->
                    <g transform="translate(100, 250)">
                        <!-- Axes -->
                        <line x1="0" y1="0" x2="400" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="0" x2="0" y2="-200" stroke="#34495e" stroke-width="2"/>

                        <!-- Method bars -->
                        <rect x="20" y="-160" width="40" height="160" fill="#e74c3c" opacity="0.8"/>
                        <text x="40" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">scCODA</text>
                        <text x="40" y="-170" font-size="12" fill="#2c3e50" text-anchor="middle" font-weight="bold">0.64</text>

                        <rect x="80" y="-120" width="40" height="120" fill="#3498db" opacity="0.8"/>
                        <text x="100" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">DirMult</text>
                        <text x="100" y="-130" font-size="12" fill="#2c3e50" text-anchor="middle">0.48</text>

                        <rect x="140" y="-100" width="40" height="100" fill="#27ae60" opacity="0.8"/>
                        <text x="160" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">ALDEx2</text>
                        <text x="160" y="-110" font-size="12" fill="#2c3e50" text-anchor="middle">0.40</text>

                        <rect x="200" y="-80" width="40" height="80" fill="#f39c12" opacity="0.8"/>
                        <text x="220" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">ANCOM</text>
                        <text x="220" y="-90" font-size="12" fill="#2c3e50" text-anchor="middle">0.32</text>

                        <rect x="260" y="-60" width="40" height="60" fill="#9b59b6" opacity="0.8"/>
                        <text x="280" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">t-test</text>
                        <text x="280" y="-70" font-size="12" fill="#2c3e50" text-anchor="middle">0.24</text>

                        <rect x="320" y="-40" width="40" height="40" fill="#95a5a6" opacity="0.8"/>
                        <text x="340" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">Poisson</text>
                        <text x="340" y="-50" font-size="12" fill="#2c3e50" text-anchor="middle">0.16</text>

                        <!-- Labels -->
                        <text x="200" y="40" font-size="12" fill="#34495e" text-anchor="middle">Methods</text>
                        <text x="-30" y="-100" font-size="12" fill="#34495e" text-anchor="middle" transform="rotate(-90 -30 -100)">MCC Score</text>

                        <!-- Title -->
                        <text x="200" y="-220" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                            Average Performance (Low Sample Size)
                        </text>
                    </g>
                </svg>
            </div>

            <h3>Key Performance Metrics</h3>

            <table>
                <tr>
                    <th>Metric</th>
                    <th>scCODA</th>
                    <th>Best Alternative</th>
                    <th>Advantage</th>
                </tr>
                <tr>
                    <td><strong>Matthews Correlation Coefficient</strong></td>
                    <td>0.64</td>
                    <td>0.48 (DirMult)</td>
                    <td>33% improvement</td>
                </tr>
                <tr>
                    <td><strong>False Discovery Rate</strong></td>
                    <td>Controlled at target</td>
                    <td>Often inflated</td>
                    <td>Reliable FDR control</td>
                </tr>
                <tr>
                    <td><strong>Sensitivity (TPR)</strong></td>
                    <td>High</td>
                    <td>Lower</td>
                    <td>Better detection power</td>
                </tr>
                <tr>
                    <td><strong>Single Sample Analysis</strong></td>
                    <td>✓ Possible</td>
                    <td>✗ Not possible</td>
                    <td>Bayesian advantage</td>
                </tr>
            </table>

            <div class="model-box">
                <h4>🔹 Power Analysis Results</h4>
                <p>Sample size requirements for 80% power:</p>

                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Abundant cell types (20% of total):</strong> 5 samples for 2-fold change</li>
                    <li><strong>Rare cell types (2.5% of total):</strong> 20-30 samples for 2-fold change</li>
                    <li><strong>Large effects (16-fold change):</strong> <10 samples even for rare types</li>
                </ul>
            </div>

            <div class="solution-highlight">
                <strong>Bottom Line:</strong> scCODA provides the most reliable and powerful method for compositional analysis of single-cell data, with proper statistical control and the ability to work with realistic sample sizes.
            </div>
        </section>
    </div>

    <script>
        // Add smooth scrolling for navigation
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to step counters
            const stepCounters = document.querySelectorAll('.step-counter');
            stepCounters.forEach(counter => {
                counter.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(360deg)';
                    this.style.transition = 'transform 0.5s';
                });

                counter.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });

            // Add interactive code block functionality
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.addEventListener('click', function() {
                    // Select all text in the code block
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Show notification
                    showNotification('Code copied to clipboard!');
                });
            });

            // Add method box interactions
            const methodBoxes = document.querySelectorAll('.model-box, .bayesian-box');
            methodBoxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.transition = 'transform 0.3s ease';
                    this.style.boxShadow = '0 4px 12px rgba(255, 107, 107, 0.2)';
                });

                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // Add progress indicator for navigation
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');

            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });

            // Add table row hover effects
            const tableRows = document.querySelectorAll('tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#e8f5e8';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });

            // Add visualization hover effects
            const visualizations = document.querySelectorAll('.visualization');
            visualizations.forEach(viz => {
                viz.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.3s ease';
                    this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.15)';
                });

                viz.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                });
            });
        });

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                max-width: 80%;
                text-align: center;
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS for active navigation and enhanced interactivity
        const style = document.createElement('style');
        style.textContent = `
            .navigation a.active {
                color: #ff6b6b !important;
                font-weight: bold;
            }

            .navigation a.active::before {
                content: "→ ";
                color: #ff6b6b;
            }

            .formula-box:hover {
                box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                transition: box-shadow 0.3s ease;
                transform: scale(1.02);
            }

            .code-block:hover {
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block::after {
                content: "Click to select all";
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.1);
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
                white-space: nowrap;
            }

            .code-block:hover::after {
                opacity: 1;
            }

            .highlight-box:hover {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }

            .problem-highlight:hover, .solution-highlight:hover {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }

            svg path, svg rect, svg circle {
                transition: all 0.3s ease;
            }

            svg:hover path[stroke="#e74c3c"] {
                stroke-width: 4;
            }

            svg:hover rect[fill="#3498db"] {
                fill: #2980b9;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
