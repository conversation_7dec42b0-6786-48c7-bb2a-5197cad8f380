<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fundamental Concepts of Compositional Data Analysis Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
            color: white;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #ff6b6b;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #4ecdc4;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #ffb3ba 0%, #ffdfba 100%);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        
        .formula-box {
            background: #f1f2f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            line-height: 1.4;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-counter {
            background: #ff6b6b;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }
        
        .navigation ul {
            list-style: none;
        }
        
        .navigation li {
            margin: 5px 0;
        }
        
        .navigation a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .navigation a:hover {
            color: #ff6b6b;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 107, 107, 0.4);
        }
        
        table {
            border-collapse: collapse;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            width: 100%;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .data-table {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .equivalence-demo {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#definition">Definition</a></li>
            <li><a href="#subcomposition">Subcomposition</a></li>
            <li><a href="#closure">Closure Operation</a></li>
            <li><a href="#equivalence">Equivalence Classes</a></li>
            <li><a href="#perturbation">Perturbation</a></li>
            <li><a href="#amalgamation">Amalgamation</a></li>
            <li><a href="#missing">Missing Values</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>📊 Compositional Data Analysis</h1>
            <p>Fundamental Concepts and Practical Applications</p>
        </div>

        <section id="introduction" class="section">
            <h2>🔬 Introduction to Compositional Data</h2>
            
            <div class="highlight-box">
                <strong>What is Compositional Data?</strong><br>
                Compositional data represents parts of a whole, where the relative proportions matter more than absolute values. Examples include chemical compositions, budget allocations, time use studies, and nutritional content analysis.
            </div>

            <h3>Key Characteristics</h3>
            <ul style="margin: 20px 0; padding-left: 30px;">
                <li><strong>Relative Nature:</strong> Only ratios between components are meaningful</li>
                <li><strong>Constant Sum:</strong> Components sum to a fixed total (1, 100%, 10⁶ ppm)</li>
                <li><strong>Positive Components:</strong> All parts must be non-negative</li>
                <li><strong>Closure Constraint:</strong> Adding/removing components affects all others</li>
            </ul>

            <div class="visualization">
                <h4>Compositional Data Examples</h4>
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Nutrition composition pie chart -->
                    <g transform="translate(175, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Nutritional Composition</text>
                        
                        <!-- Pie chart for soy -->
                        <circle cx="0" cy="0" r="60" fill="#3498db" opacity="0.8"/>
                        <path d="M 0 -60 A 60 60 0 0 1 42 -42 L 0 0 Z" fill="#e74c3c" opacity="0.8"/>
                        <path d="M 42 -42 A 60 60 0 0 1 30 52 L 0 0 Z" fill="#27ae60" opacity="0.8"/>
                        <path d="M 30 52 A 60 60 0 0 1 0 -60 L 0 0 Z" fill="#f39c12" opacity="0.8"/>
                        
                        <!-- Labels -->
                        <text x="0" y="85" font-size="10" fill="#2c3e50" text-anchor="middle">Fat: 23%</text>
                        <text x="0" y="100" font-size="10" fill="#2c3e50" text-anchor="middle">Carbs: 33%</text>
                        <text x="0" y="115" font-size="10" fill="#2c3e50" text-anchor="middle">Protein: 43%</text>
                    </g>
                    
                    <!-- Budget allocation -->
                    <g transform="translate(525, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Budget Allocation</text>
                        
                        <!-- Stacked bar chart -->
                        <rect x="-50" y="-60" width="100" height="15" fill="#3498db" opacity="0.8"/>
                        <rect x="-50" y="-40" width="100" height="15" fill="#e74c3c" opacity="0.8"/>
                        <rect x="-50" y="-20" width="100" height="15" fill="#27ae60" opacity="0.8"/>
                        <rect x="-50" y="0" width="100" height="15" fill="#f39c12" opacity="0.8"/>
                        <rect x="-50" y="20" width="100" height="15" fill="#9b59b6" opacity="0.8"/>
                        
                        <!-- Labels -->
                        <text x="60" y="-50" font-size="10" fill="#2c3e50">Housing: 30%</text>
                        <text x="60" y="-30" font-size="10" fill="#2c3e50">Food: 25%</text>
                        <text x="60" y="-10" font-size="10" fill="#2c3e50">Transport: 20%</text>
                        <text x="60" y="10" font-size="10" fill="#2c3e50">Entertainment: 15%</text>
                        <text x="60" y="30" font-size="10" fill="#2c3e50">Savings: 10%</text>
                    </g>
                    
                    <!-- Arrow showing constraint -->
                    <path d="M 350 50 Q 400 30 450 50" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#constraintArrow)"/>
                    <text x="400" y="25" font-size="12" fill="#e74c3c" text-anchor="middle" font-weight="bold">Constant Sum Constraint</text>
                    
                    <defs>
                        <marker id="constraintArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="warning-box">
                <strong>⚠️ Common Pitfall:</strong> Traditional statistical methods often fail with compositional data because they ignore the constant sum constraint, leading to spurious correlations and incorrect interpretations.
            </div>
        </section>

        <section id="definition" class="section">
            <h2>📋 Definition and Kitchen Example</h2>

            <div class="concept-box">
                <strong>Formal Definition:</strong> A composition is a vector of D positive components $\mathbf{x} = (x_1, x_2, \ldots, x_D)$ summing up to a given constant $\kappa$, typically set to 1 (proportions), 100 (percentages), or $10^6$ (ppm).
            </div>

            <h3>Real-World Example: Kitchen Nutrition Data</h3>
            <p>Let's examine nutritional data from common food items to understand compositional concepts:</p>

            <div class="visualization">
                <h4>Original Kitchen Dataset</h4>
                <table class="data-table">
                    <tr>
                        <th>Food Item</th>
                        <th>Fat (g)</th>
                        <th>Sodium (mg)</th>
                        <th>Carbohydrates (g)</th>
                        <th>Protein (g)</th>
                        <th>Total (g)</th>
                    </tr>
                    <tr>
                        <td><strong>Soy</strong></td>
                        <td>7</td>
                        <td>14</td>
                        <td>10</td>
                        <td>13</td>
                        <td>100</td>
                    </tr>
                    <tr>
                        <td><strong>Peas</strong></td>
                        <td>3</td>
                        <td>12</td>
                        <td>61</td>
                        <td>22</td>
                        <td>100</td>
                    </tr>
                    <tr>
                        <td><strong>Wheat</strong></td>
                        <td>1</td>
                        <td>15</td>
                        <td>60</td>
                        <td>23</td>
                        <td>100</td>
                    </tr>
                    <tr>
                        <td><strong>Corn</strong></td>
                        <td>1</td>
                        <td>180</td>
                        <td>15</td>
                        <td>2</td>
                        <td>82</td>
                    </tr>
                    <tr>
                        <td><strong>Beans</strong></td>
                        <td>0.5</td>
                        <td>680</td>
                        <td>30</td>
                        <td>12</td>
                        <td>NA</td>
                    </tr>
                </table>
            </div>

            <div class="code-block">
# Loading data in R with compositions package
library(compositions)
kitchen <- read.table("SimpleData.txt", sep="")

# Display the raw data
print(kitchen)
            </div>

            <h3>The Interpretation Problem</h3>

            <div class="warning-box">
                <strong>Naive Interpretation Issues:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Beans appear to have 4× more sodium than corn (680 vs 180 mg)</li>
                    <li>Beans seem to have 2× more carbohydrates than corn (30 vs 15 g)</li>
                    <li>Soy and corn appear poor in protein and carbohydrates</li>
                </ul>
                <strong>Problem:</strong> These conclusions contradict nutritional knowledge because we're comparing absolute amounts without considering the compositional nature!
            </div>

            <div class="visualization">
                <h4>Why Direct Comparison Fails</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Corn composition -->
                    <g transform="translate(150, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Corn (82g total)</text>

                        <!-- Bar chart -->
                        <rect x="-60" y="-60" width="15" height="15" fill="#f39c12"/>
                        <text x="-52" y="-40" font-size="10" fill="#2c3e50" text-anchor="middle">Fat: 1g</text>

                        <rect x="-40" y="-60" width="15" height="180" fill="#e74c3c"/>
                        <text x="-32" y="130" font-size="10" fill="#2c3e50" text-anchor="middle">Na: 180mg</text>

                        <rect x="-20" y="-60" width="15" height="30" fill="#27ae60"/>
                        <text x="-12" y="-25" font-size="10" fill="#2c3e50" text-anchor="middle">Carb: 15g</text>

                        <rect x="0" y="-60" width="15" height="4" fill="#3498db"/>
                        <text x="8" y="-50" font-size="10" fill="#2c3e50" text-anchor="middle">Prot: 2g</text>

                        <rect x="20" y="-60" width="15" height="64" fill="#95a5a6"/>
                        <text x="28" y="15" font-size="10" fill="#2c3e50" text-anchor="middle">Other: 64g</text>
                    </g>

                    <!-- Beans composition -->
                    <g transform="translate(450, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Beans (?g total)</text>

                        <!-- Bar chart -->
                        <rect x="-60" y="-60" width="15" height="1" fill="#f39c12"/>
                        <text x="-52" y="-50" font-size="10" fill="#2c3e50" text-anchor="middle">Fat: 0.5g</text>

                        <rect x="-40" y="-60" width="15" height="340" fill="#e74c3c"/>
                        <text x="-32" y="290" font-size="10" fill="#2c3e50" text-anchor="middle">Na: 680mg</text>

                        <rect x="-20" y="-60" width="15" height="60" fill="#27ae60"/>
                        <text x="-12" y="10" font-size="10" fill="#2c3e50" text-anchor="middle">Carb: 30g</text>

                        <rect x="0" y="-60" width="15" height="24" fill="#3498db"/>
                        <text x="8" y="-30" font-size="10" fill="#2c3e50" text-anchor="middle">Prot: 12g</text>

                        <rect x="20" y="-60" width="15" height="100" fill="#95a5a6"/>
                        <text x="28" y="50" font-size="10" fill="#2c3e50" text-anchor="middle">Other: ?g</text>
                    </g>

                    <!-- Problem annotation -->
                    <text x="300" y="250" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">
                        Problem: Different totals make direct comparison meaningless!
                    </text>
                </svg>
            </div>

            <div class="concept-box">
                <strong>Key Insight:</strong> The fundamental issue is that we're dealing with <em>parts of different wholes</em>. Compositional analysis focuses on the <strong>relative relationships</strong> between components, not their absolute amounts.
            </div>
        </section>

        <section id="subcomposition" class="section">
            <h2>🔄 Subcompositions and Closure Operation</h2>

            <div class="highlight-box">
                <strong>Subcomposition:</strong> A composition representing only some of the possible components. Most real compositional data represents subcompositions since we rarely analyze every possible component.
            </div>

            <h3>The Closure Problem</h3>
            <p>Our kitchen data doesn't sum to 100% because it excludes water, vitamins, and other minerals. For soy: 7 + 14 + 10 + 13 = 44g out of 100g, leaving 56g unaccounted for.</p>

            <div class="concept-box">
                <span class="step-counter">1</span>
                <strong>Focus on Components of Interest</strong>
                <p>We're interested in the fat-protein-carbohydrate composition for dietary analysis. Sodium intake is more influenced by drinking and salting habits than food choice.</p>
            </div>

            <div class="code-block">
# Select components of interest and close to 100%
coi <- clo(kitchen, parts = c("Fat", "Carbonates", "Protein"), total = 100)
print(coi)

#           Fat  Carbonates  Protein
# soy      23.33    33.33    43.33
# peas      3.49    70.93    25.58
# wheat     1.19    71.43    27.38
# corn      5.56    83.33    11.11
# beans     1.18    70.59    28.24
            </div>

            <div class="visualization">
                <h4>Before and After Closure</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Before closure -->
                    <g transform="translate(175, 200)">
                        <text x="0" y="-150" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Before Closure (Raw Data)</text>

                        <!-- Soy composition -->
                        <text x="-100" y="-120" font-size="12" fill="#2c3e50" font-weight="bold">Soy</text>
                        <rect x="-100" y="-100" width="7" height="20" fill="#f39c12"/>
                        <text x="-96" y="-75" font-size="10" fill="#2c3e50">Fat: 7g</text>

                        <rect x="-90" y="-100" width="10" height="20" fill="#27ae60"/>
                        <text x="-85" y="-75" font-size="10" fill="#2c3e50">Carb: 10g</text>

                        <rect x="-75" y="-100" width="13" height="20" fill="#3498db"/>
                        <text x="-68" y="-75" font-size="10" fill="#2c3e50">Prot: 13g</text>

                        <rect x="-60" y="-100" width="56" height="20" fill="#95a5a6"/>
                        <text x="-32" y="-75" font-size="10" fill="#2c3e50">Other: 70g</text>

                        <!-- Total line -->
                        <line x1="-100" y1="-60" x2="0" y2="-60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="-50" y="-45" font-size="12" fill="#2c3e50" text-anchor="middle">Total: 100g</text>

                        <!-- Corn composition -->
                        <text x="-100" y="0" font-size="12" fill="#2c3e50" font-weight="bold">Corn</text>
                        <rect x="-100" y="20" width="1" height="20" fill="#f39c12"/>
                        <text x="-99" y="45" font-size="10" fill="#2c3e50">Fat: 1g</text>

                        <rect x="-95" y="20" width="15" height="20" fill="#27ae60"/>
                        <text x="-87" y="45" font-size="10" fill="#2c3e50">Carb: 15g</text>

                        <rect x="-75" y="20" width="2" height="20" fill="#3498db"/>
                        <text x="-74" y="45" font-size="10" fill="#2c3e50">Prot: 2g</text>

                        <rect x="-70" y="20" width="64" height="20" fill="#95a5a6"/>
                        <text x="-38" y="45" font-size="10" fill="#2c3e50">Other: 64g</text>

                        <!-- Total line -->
                        <line x1="-100" y1="60" x2="-18" y2="60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="-59" y="75" font-size="12" fill="#2c3e50" text-anchor="middle">Total: 82g</text>
                    </g>

                    <!-- Arrow -->
                    <path d="M 350 200 L 450 200" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#closureArrow)"/>
                    <text x="400" y="190" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">Closure</text>
                    <text x="400" y="210" font-size="12" fill="#e74c3c" text-anchor="middle">clo()</text>

                    <!-- After closure -->
                    <g transform="translate(525, 200)">
                        <text x="0" y="-150" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">After Closure (Proportions)</text>

                        <!-- Soy composition -->
                        <text x="-100" y="-120" font-size="12" fill="#2c3e50" font-weight="bold">Soy</text>
                        <rect x="-100" y="-100" width="23" height="20" fill="#f39c12"/>
                        <text x="-88" y="-75" font-size="10" fill="#2c3e50">Fat: 23%</text>

                        <rect x="-75" y="-100" width="33" height="20" fill="#27ae60"/>
                        <text x="-58" y="-75" font-size="10" fill="#2c3e50">Carb: 33%</text>

                        <rect x="-40" y="-100" width="43" height="20" fill="#3498db"/>
                        <text x="-18" y="-75" font-size="10" fill="#2c3e50">Prot: 43%</text>

                        <!-- Total line -->
                        <line x1="-100" y1="-60" x2="5" y2="-60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="-47" y="-45" font-size="12" fill="#2c3e50" text-anchor="middle">Total: 100%</text>

                        <!-- Corn composition -->
                        <text x="-100" y="0" font-size="12" fill="#2c3e50" font-weight="bold">Corn</text>
                        <rect x="-100" y="20" width="6" height="20" fill="#f39c12"/>
                        <text x="-97" y="45" font-size="10" fill="#2c3e50">Fat: 6%</text>

                        <rect x="-92" y="20" width="83" height="20" fill="#27ae60"/>
                        <text x="-50" y="45" font-size="10" fill="#2c3e50">Carb: 83%</text>

                        <rect x="-7" y="20" width="11" height="20" fill="#3498db"/>
                        <text x="-1" y="45" font-size="10" fill="#2c3e50">Prot: 11%</text>

                        <!-- Total line -->
                        <line x1="-100" y1="60" x2="5" y2="60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="-47" y="75" font-size="12" fill="#2c3e50" text-anchor="middle">Total: 100%</text>
                    </g>

                    <defs>
                        <marker id="closureArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Dramatic Interpretation Changes</h3>

            <div class="warning-box">
                <strong>⚠️ Ranking Reversals After Closure:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Protein ranking:</strong> Before closure: wheat > peas > soy > corn. After closure: soy > wheat > peas > corn</li>
                    <li><strong>Carbohydrate comparison:</strong> Corn now appears richer in carbohydrates than beans (83% vs 71%)</li>
                    <li><strong>Soy protein:</strong> Jumps from middle ranking to highest (43%)</li>
                </ul>
                <strong>Lesson:</strong> Conclusions can be opposite depending on which composition we observe!
            </div>

            <h3>Subcompositional Coherence Principle</h3>

            <div class="concept-box">
                <strong>Aitchison's Principle:</strong> Any compositional data analysis should yield the same results whether we analyze a subcomposition directly or extract it from a larger composition. Traditional multivariate statistics violate this principle.
            </div>

            <div class="formula-box">
                <strong>Mathematical Closure Operation:</strong>
                $$\text{clo}(\mathbf{x}) = \frac{\kappa}{\sum_{i=1}^D x_i} \cdot \mathbf{x}$$
                where $\kappa$ is the desired total (typically 1 or 100).
            </div>
        </section>

        <section id="equivalence" class="section">
            <h2>⚖️ Compositions as Equivalence Classes</h2>

            <div class="highlight-box">
                <strong>Key Insight:</strong> Compositions represent equivalence classes where vectors that are positive multiples of each other represent the same composition. The total amount is irrelevant - only relative proportions matter.
            </div>

            <h3>Compositional Equivalence</h3>

            <div class="formula-box">
                <strong>Equivalence Relation:</strong>
                $$\mathbf{a} \sim_D \mathbf{b} \iff \exists s > 0 \text{ for all } i: a_i = s \cdot b_i$$
                Two vectors are compositionally equivalent if one is a positive scalar multiple of the other.
            </div>

            <div class="equivalence-demo">
                <h4>Example: Three Equivalent Representations of Corn</h4>

                <div class="code-block">
# Three equivalent representations of corn composition
# 1. As portions of 100g (mass %)
corn_100g <- c(Fat=1.22, Carbonates=18.29, Protein=2.44, Other=78.05)

# 2. As portions of 82g (original measurement)
corn_82g <- c(Fat=1, Carbonates=15, Protein=2, Other=64)

# 3. As relative portions summing to 1
corn_relative <- acomp(c(Fat=0.0122, Carbonates=0.1829, Protein=0.0244, Other=0.7805))

# All represent the same composition!
print("All three are compositionally equivalent")
                </div>
            </div>

            <div class="visualization">
                <h4>Equivalence Class Visualization</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Three equivalent representations -->
                    <g transform="translate(100, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">100g Total</text>

                        <!-- Pie chart scaled to 100 -->
                        <circle cx="0" cy="0" r="50" fill="#95a5a6" opacity="0.8"/>
                        <path d="M 0 -50 A 50 50 0 0 1 8 -49 L 0 0 Z" fill="#f39c12" opacity="0.8"/>
                        <path d="M 8 -49 A 50 50 0 0 1 35 35 L 0 0 Z" fill="#27ae60" opacity="0.8"/>
                        <path d="M 35 35 A 50 50 0 0 1 5 50 L 0 0 Z" fill="#3498db" opacity="0.8"/>

                        <text x="0" y="70" font-size="10" fill="#2c3e50" text-anchor="middle">1.22g Fat</text>
                        <text x="0" y="85" font-size="10" fill="#2c3e50" text-anchor="middle">18.29g Carb</text>
                        <text x="0" y="100" font-size="10" fill="#2c3e50" text-anchor="middle">2.44g Protein</text>
                    </g>

                    <g transform="translate(300, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">82g Total</text>

                        <!-- Pie chart scaled to 82 -->
                        <circle cx="0" cy="0" r="41" fill="#95a5a6" opacity="0.8"/>
                        <path d="M 0 -41 A 41 41 0 0 1 7 -40 L 0 0 Z" fill="#f39c12" opacity="0.8"/>
                        <path d="M 7 -40 A 41 41 0 0 1 29 29 L 0 0 Z" fill="#27ae60" opacity="0.8"/>
                        <path d="M 29 29 A 41 41 0 0 1 4 41 L 0 0 Z" fill="#3498db" opacity="0.8"/>

                        <text x="0" y="70" font-size="10" fill="#2c3e50" text-anchor="middle">1g Fat</text>
                        <text x="0" y="85" font-size="10" fill="#2c3e50" text-anchor="middle">15g Carb</text>
                        <text x="0" y="100" font-size="10" fill="#2c3e50" text-anchor="middle">2g Protein</text>
                    </g>

                    <g transform="translate(500, 150)">
                        <text x="0" y="-100" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Unit Total</text>

                        <!-- Pie chart scaled to 1 -->
                        <circle cx="0" cy="0" r="50" fill="#95a5a6" opacity="0.8"/>
                        <path d="M 0 -50 A 50 50 0 0 1 8 -49 L 0 0 Z" fill="#f39c12" opacity="0.8"/>
                        <path d="M 8 -49 A 50 50 0 0 1 35 35 L 0 0 Z" fill="#27ae60" opacity="0.8"/>
                        <path d="M 35 35 A 50 50 0 0 1 5 50 L 0 0 Z" fill="#3498db" opacity="0.8"/>

                        <text x="0" y="70" font-size="10" fill="#2c3e50" text-anchor="middle">0.0122 Fat</text>
                        <text x="0" y="85" font-size="10" fill="#2c3e50" text-anchor="middle">0.1829 Carb</text>
                        <text x="0" y="100" font-size="10" fill="#2c3e50" text-anchor="middle">0.0244 Protein</text>
                    </g>

                    <!-- Equivalence arrows -->
                    <path d="M 150 150 L 250 150" stroke="#27ae60" stroke-width="2" fill="none" marker-end="url(#equivArrow)"/>
                    <path d="M 350 150 L 450 150" stroke="#27ae60" stroke-width="2" fill="none" marker-end="url(#equivArrow)"/>

                    <text x="200" y="140" font-size="12" fill="#27ae60" text-anchor="middle">≡</text>
                    <text x="400" y="140" font-size="12" fill="#27ae60" text-anchor="middle">≡</text>

                    <text x="300" y="250" font-size="14" fill="#27ae60" text-anchor="middle" font-weight="bold">
                        Same Composition - Different Scales
                    </text>

                    <defs>
                        <marker id="equivArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#27ae60"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>The acomp Class in R</h3>

            <div class="concept-box">
                <span class="step-counter">1</span>
                <strong>Automatic Closure</strong>
                <p>The <code>acomp()</command tells R to treat data as compositional, automatically performing closure to sum to 1.</p>
            </div>

            <div class="code-block">
# Using acomp for compositional data
corn_amounts <- c(Fat=1, Carbonates=15, Protein=2, Other=64)
corn_composition <- acomp(corn_amounts)

print(corn_composition)
#        Fat  Carbonates    Protein      Other
# 0.01220    0.18293    0.02439    0.78049
# attr(,"class")
# [1] "acomp"

# The ratios are preserved regardless of scale
print("Carb/Fat ratio:", corn_composition["Carbonates"] / corn_composition["Fat"])
# Always equals 15 regardless of total scale
            </div>

            <h3>Information Preservation</h3>

            <div class="concept-box">
                <strong>What's Preserved:</strong> All ratios between components remain constant across equivalent representations.
                <br><br>
                <strong>What's Lost:</strong> Information about the absolute total amount of the sample.
            </div>

            <div class="warning-box">
                <strong>⚠️ Critical Point:</strong> In our kitchen example, the total amount is not relevant for evaluating nutritional character - it's an arbitrary choice of the producer or measurement unit. We can rescale each sample by any positive value without changing the compositional information.
            </div>
        </section>

        <section id="perturbation" class="section">
            <h2>🔄 Perturbation: Change of Units</h2>

            <div class="highlight-box">
                <strong>Perturbation:</strong> The fundamental operation in compositional data analysis, equivalent to vector addition in real vector space. It allows us to change units or apply multiplicative transformations while preserving compositional structure.
            </div>

            <h3>From Mass to Energy Composition</h3>
            <p>Mass percentage is often less relevant than energy intake. Different nutrients have different energy content (kJ/g):</p>

            <div class="code-block">
# Energy content per gram (as a composition)
epm <- acomp(c(Fat=37, Protein=17, Carbonates=17))
print(epm)
#       Fat   Protein Carbonates
#   0.5211    0.2394     0.2394

# This represents the relative energy contribution per unit mass
            </div>

            <h3>Mathematical Definition</h3>

            <div class="formula-box">
                <strong>Perturbation Operation:</strong>
                $$(\mathbf{x} \oplus \mathbf{y})_i = \mathcal{C}\left[\frac{x_i \cdot y_i}{\sum_{j=1}^D x_j \cdot y_j}\right]$$
                where $\mathcal{C}[\cdot]$ denotes closure to the desired total.
            </div>

            <div class="concept-box">
                <span class="step-counter">1</span>
                <strong>Step-by-Step Perturbation Example</strong>
                <p>Transform soy mass composition to energy composition:</p>

                <div class="formula-box">
                    $$\text{ec}_1 = \mathcal{C}[(23.33 \times 0.52, 33.33 \times 0.24, 43.33 \times 0.24)]$$
                    $$= \mathcal{C}[(12.13, 8.00, 10.40)]$$
                    $$= (0.398, 0.262, 0.340)$$
                </div>
            </div>

            <div class="visualization">
                <h4>Perturbation Transformation: Mass → Energy</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Original mass composition -->
                    <g transform="translate(150, 200)">
                        <text x="0" y="-150" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Mass Composition (%)</text>

                        <!-- Soy mass composition -->
                        <text x="-80" y="-120" font-size="12" fill="#2c3e50" font-weight="bold">Soy</text>
                        <rect x="-80" y="-100" width="23" height="30" fill="#f39c12"/>
                        <text x="-68" y="-70" font-size="10" fill="#2c3e50">Fat: 23%</text>

                        <rect x="-55" y="-100" width="33" height="30" fill="#27ae60"/>
                        <text x="-38" y="-70" font-size="10" fill="#2c3e50">Carb: 33%</text>

                        <rect x="-20" y="-100" width="43" height="30" fill="#3498db"/>
                        <text x="1" y="-70" font-size="10" fill="#2c3e50">Prot: 43%</text>

                        <!-- Energy per mass -->
                        <text x="-80" y="0" font-size="12" fill="#2c3e50" font-weight="bold">Energy/Mass</text>
                        <rect x="-80" y="20" width="52" height="30" fill="#f39c12" opacity="0.7"/>
                        <text x="-54" y="50" font-size="10" fill="#2c3e50">Fat: 37 kJ/g</text>

                        <rect x="-25" y="20" width="24" height="30" fill="#27ae60" opacity="0.7"/>
                        <text x="-13" y="50" font-size="10" fill="#2c3e50">Carb: 17</text>

                        <rect x="2" y="20" width="24" height="30" fill="#3498db" opacity="0.7"/>
                        <text x="14" y="50" font-size="10" fill="#2c3e50">Prot: 17</text>
                    </g>

                    <!-- Perturbation symbol -->
                    <g transform="translate(350, 200)">
                        <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.2"/>
                        <text x="0" y="5" font-size="24" fill="#e74c3c" text-anchor="middle" font-weight="bold">⊕</text>
                        <text x="0" y="-50" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">Perturbation</text>
                        <text x="0" y="50" font-size="12" fill="#e74c3c" text-anchor="middle">coi ⊕ epm</text>
                    </g>

                    <!-- Result energy composition -->
                    <g transform="translate(550, 200)">
                        <text x="0" y="-150" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Energy Composition (%)</text>

                        <!-- Soy energy composition -->
                        <text x="-80" y="-120" font-size="12" fill="#2c3e50" font-weight="bold">Soy</text>
                        <rect x="-80" y="-100" width="40" height="30" fill="#f39c12"/>
                        <text x="-60" y="-70" font-size="10" fill="#2c3e50">Fat: 40%</text>

                        <rect x="-38" y="-100" width="26" height="30" fill="#27ae60"/>
                        <text x="-25" y="-70" font-size="10" fill="#2c3e50">Carb: 26%</text>

                        <rect x="-10" y="-100" width="34" height="30" fill="#3498db"/>
                        <text x="7" y="-70" font-size="10" fill="#2c3e50">Prot: 34%</text>

                        <!-- Calculation steps -->
                        <text x="-80" y="20" font-size="10" fill="#7f8c8d">23×37 = 851</text>
                        <text x="-80" y="35" font-size="10" fill="#7f8c8d">33×17 = 561</text>
                        <text x="-80" y="50" font-size="10" fill="#7f8c8d">43×17 = 731</text>
                        <text x="-80" y="70" font-size="10" fill="#7f8c8d">Total: 2143</text>
                    </g>

                    <!-- Arrows -->
                    <path d="M 200 200 L 300 200" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#perturbArrow)"/>
                    <path d="M 400 200 L 500 200" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#perturbArrow)"/>

                    <defs>
                        <marker id="perturbArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>R Implementation</h3>

            <div class="code-block">
# Perturbation in R using + operator for acomp objects
coi <- acomp(c(Fat=23.33, Carbonates=33.33, Protein=43.33))
epm <- acomp(c(Fat=37, Protein=17, Carbonates=17))

# Perform perturbation
ec <- coi + epm  # This is the perturbation operation
print(ec)

#       Fat Carbonates   Protein
#   0.39846   0.26154   0.34000

# Now ec contains energy proportions instead of mass proportions
            </code>

            <h3>Properties of Perturbation</h3>

            <div class="concept-box">
                <strong>Vector Space Properties:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Commutativity:</strong> $\mathbf{x} \oplus \mathbf{y} = \mathbf{y} \oplus \mathbf{x}$</li>
                    <li><strong>Associativity:</strong> $(\mathbf{x} \oplus \mathbf{y}) \oplus \mathbf{z} = \mathbf{x} \oplus (\mathbf{y} \oplus \mathbf{z})$</li>
                    <li><strong>Neutral element:</strong> $\mathbf{n} = (1/D, 1/D, \ldots, 1/D)$</li>
                    <li><strong>Inverse element:</strong> $\ominus\mathbf{x} = (1/x_1, 1/x_2, \ldots, 1/x_D)$ (after closure)</li>
                </ul>
            </div>

            <div class="warning-box">
                <strong>⚠️ Important:</strong> Perturbation is as fundamental to compositional data as vector addition is to real vector space. It's the correct way to combine or transform compositional information while preserving the compositional structure.
            </div>
        </section>

        <section id="amalgamation" class="section">
            <h2>🔗 Amalgamation</h2>

            <div class="highlight-box">
                <strong>Amalgamation:</strong> The process of combining several components into a single component. While common, it's a dangerous operation that permanently loses information.
            </div>

            <h3>Example: Fat Types</h3>
            <p>Original data might distinguish between saturated and unsaturated fats:</p>

            <div class="code-block">
# Original detailed fat data
fatDataset <- data.frame(
  total.Fat = c(7, 3, 1, 1, 0.5),
  sat.Fat = c(1.40, 0.60, 0.25, 0.32, 0.10),
  unsat.Fat = c(5.60, 2.40, 0.75, 0.68, 0.10),
  row.names = c("soy", "peas", "wheat", "corn", "beans")
)

# Amalgamation: combine into total fat
fatDataset$total.Fat <- totals(aplus(fatDataset[, c("sat.Fat", "unsat.Fat")]))
            </div>

            <div class="warning-box">
                <strong>⚠️ Information Loss:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Cannot recover individual fat types after amalgamation</li>
                    <li>If saturated and unsaturated fats have different energy content, we lose the ability to compute accurate energy compositions</li>
                    <li>Different cultural or health perspectives on fat types are lost</li>
                </ul>
            </div>

            <h3>When to Amalgamate</h3>

            <div class="concept-box">
                <strong>Safe Amalgamation Guidelines:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Problem Definition Stage:</strong> Only during initial variable selection</li>
                    <li><strong>Meaningful Units:</strong> Must make sense in the units you're working with</li>
                    <li><strong>Question Alignment:</strong> Should connect deeply with your research questions</li>
                    <li><strong>No Post-Analysis:</strong> Never amalgamate after analysis has begun</li>
                </ul>
            </div>
        </section>

        <section id="missing" class="section">
            <h2>❓ Missing Values and Outliers</h2>

            <div class="highlight-box">
                <strong>Critical Challenge:</strong> Missing values in compositional data are more problematic than in classical multivariate analysis because a missing value in one component affects the interpretation of all others.
            </div>

            <h3>The Missing Value Problem</h3>

            <div class="warning-box">
                <strong>Cascading Effects:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>A missing value in a single component makes closure impossible</li>
                    <li>The actual proportion of no component is known when any component is missing</li>
                    <li>Errors propagate to all variables through the closure operation</li>
                    <li>Traditional imputation methods may violate compositional constraints</li>
                </ul>
            </div>

            <div class="visualization">
                <h4>Missing Value Impact</h4>
                <svg width="600" height="250" viewBox="0 0 600 250">
                    <!-- Complete data -->
                    <g transform="translate(150, 125)">
                        <text x="0" y="-80" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Complete Data</text>

                        <circle cx="0" cy="0" r="50" fill="#27ae60" opacity="0.8"/>
                        <path d="M 0 -50 A 50 50 0 0 1 35 35 L 0 0 Z" fill="#3498db" opacity="0.8"/>
                        <path d="M 35 35 A 50 50 0 0 1 -35 35 L 0 0 Z" fill="#e74c3c" opacity="0.8"/>
                        <path d="M -35 35 A 50 50 0 0 1 0 -50 L 0 0 Z" fill="#f39c12" opacity="0.8"/>

                        <text x="0" y="70" font-size="10" fill="#27ae60" text-anchor="middle">✓ All proportions known</text>
                    </g>

                    <!-- Arrow -->
                    <path d="M 250 125 L 350 125" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#missingArrow)"/>
                    <text x="300" y="115" font-size="12" fill="#e74c3c" text-anchor="middle">Missing Value</text>

                    <!-- Missing data -->
                    <g transform="translate(450, 125)">
                        <text x="0" y="-80" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Missing Data</text>

                        <circle cx="0" cy="0" r="50" fill="#95a5a6" opacity="0.4"/>
                        <path d="M 0 -50 A 50 50 0 0 1 35 35 L 0 0 Z" fill="#3498db" opacity="0.8"/>
                        <path d="M 35 35 A 50 50 0 0 1 -35 35 L 0 0 Z" fill="#e74c3c" opacity="0.8"/>
                        <path d="M -35 35 A 50 50 0 0 1 0 -50 L 0 0 Z" fill="#f39c12" opacity="0.8"/>

                        <!-- Question mark for missing component -->
                        <text x="-25" y="-15" font-size="24" fill="#e74c3c" text-anchor="middle" font-weight="bold">?</text>

                        <text x="0" y="70" font-size="10" fill="#e74c3c" text-anchor="middle">✗ No proportions known</text>
                    </g>

                    <defs>
                        <marker id="missingArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Solution: Focus on Ratios</h3>

            <div class="concept-box">
                <strong>Key Insight:</strong> Log-ratios between regular (non-missing) components are unaffected by missing values or measurement errors. This forms the basis for robust compositional analysis.
            </div>

            <div class="formula-box">
                <strong>Ratio Preservation:</strong>
                $$\log\left(\frac{x_i}{x_j}\right) \text{ remains valid regardless of missing } x_k$$
            </div>

            <div class="code-block">
# The compositions package handles missing values by:
# 1. Closing non-missing parts as if missing parts are 0
# 2. Knowing this won't affect proper ratio-based analysis

# Example with beans data (missing total)
beans_partial <- c(Fat=0.5, Sodium=680, Carbonates=30, Protein=12)
# Total is unknown, but ratios between known components are preserved

# Ratio of Protein to Fat is still meaningful:
protein_fat_ratio <- beans_partial["Protein"] / beans_partial["Fat"]
# This ratio is unaffected by the missing "Other" component
            </div>

            <h3>Best Practices</h3>

            <div class="concept-box">
                <strong>Handling Missing Values:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Understand the mechanism:</strong> Why is the value missing?</li>
                    <li><strong>Use ratio-based methods:</strong> Focus on log-ratios of observed components</li>
                    <li><strong>Consider completion:</strong> Add "Other" category when total is known</li>
                    <li><strong>Avoid naive imputation:</strong> Standard methods may violate constraints</li>
                    <li><strong>Document assumptions:</strong> Be explicit about missing value treatment</li>
                </ul>
            </div>
        </section>
    </div>

    <script>
        // Add smooth scrolling for navigation
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to step counters
            const stepCounters = document.querySelectorAll('.step-counter');
            stepCounters.forEach(counter => {
                counter.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(360deg)';
                    this.style.transition = 'transform 0.5s';
                });

                counter.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });

            // Add interactive code block functionality
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.addEventListener('click', function() {
                    // Select all text in the code block
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Show notification
                    showNotification('Code copied to clipboard!');
                });
            });

            // Add concept box interactions
            const conceptBoxes = document.querySelectorAll('.concept-box, .warning-box, .equivalence-demo');
            conceptBoxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.transition = 'transform 0.3s ease';
                    this.style.boxShadow = '0 4px 12px rgba(255, 107, 107, 0.2)';
                });

                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // Add progress indicator for navigation
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');

            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });

            // Add table row hover effects
            const tableRows = document.querySelectorAll('tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#e8f5e8';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });

            // Add visualization hover effects
            const visualizations = document.querySelectorAll('.visualization');
            visualizations.forEach(viz => {
                viz.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.3s ease';
                    this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.15)';
                });

                viz.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                });
            });

            // Add formula box interactions
            const formulaBoxes = document.querySelectorAll('.formula-box');
            formulaBoxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                    this.style.boxShadow = '0 4px 12px rgba(255, 107, 107, 0.3)';
                });

                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = 'none';
                });
            });
        });

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                max-width: 80%;
                text-align: center;
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS for active navigation and enhanced interactivity
        const style = document.createElement('style');
        style.textContent = `
            .navigation a.active {
                color: #ff6b6b !important;
                font-weight: bold;
            }

            .navigation a.active::before {
                content: "→ ";
                color: #ff6b6b;
            }

            .code-block:hover {
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block::after {
                content: "Click to select all";
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.1);
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
                white-space: nowrap;
            }

            .code-block:hover::after {
                opacity: 1;
            }

            .highlight-box:hover {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }

            svg path, svg rect, svg circle {
                transition: all 0.3s ease;
            }

            svg:hover path[stroke="#e74c3c"] {
                stroke-width: 4;
            }

            svg:hover rect[fill="#3498db"] {
                fill: #2980b9;
            }

            .data-table tr:hover {
                background-color: #e8f4fd !important;
                transform: scale(1.01);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
