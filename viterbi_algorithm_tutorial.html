<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Viterbi Algorithm Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
            color: white;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #ff6b6b;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }
        
        .algorithm-step {
            background: #e8f4fd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .formula-box {
            background: #f1f2f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .matrix {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        table {
            border-collapse: collapse;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-counter {
            background: #ff6b6b;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        
        .navigation ul {
            list-style: none;
        }
        
        .navigation li {
            margin: 5px 0;
        }
        
        .navigation a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .navigation a:hover {
            color: #ff6b6b;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 107, 107, 0.4);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .hmm-component {
            background: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#algorithm">Algorithm</a></li>
            <li><a href="#mathematics">Mathematics</a></li>
            <li><a href="#graph">Viterbi Graph</a></li>
            <li><a href="#example">Example</a></li>
            <li><a href="#implementation">Implementation</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>Viterbi Algorithm</h1>
            <p>Dynamic Programming for Hidden Markov Models</p>
        </div>

        <section id="introduction" class="section">
            <h2>🔍 Introduction to Hidden Markov Models</h2>
            
            <div class="highlight-box">
                <strong>What is a Hidden Markov Model (HMM)?</strong><br>
                An HMM is a statistical model that represents systems with hidden states and observable events. It's like having a system where you can see the effects (observations) but not the underlying causes (hidden states).
            </div>

            <h3>Key Components of an HMM</h3>
            
            <div class="hmm-component">
                <strong>🎯 States (S):</strong> Hidden conditions that are not directly observable<br>
                Example: Weather conditions (Rainy, Sunny)
            </div>
            
            <div class="hmm-component">
                <strong>👁️ Observations (O):</strong> Observable events influenced by hidden states<br>
                Example: Activities (Walk, Shop, Clean)
            </div>
            
            <div class="hmm-component">
                <strong>🔄 Transition Probabilities (A):</strong> Probabilities of moving from one state to another<br>
                Formula: $a_{ij} = P(s_j | s_i)$
            </div>
            
            <div class="hmm-component">
                <strong>📡 Emission Probabilities (B):</strong> Probabilities of observing events given states<br>
                Formula: $b_j(o_t) = P(o_t | s_j)$
            </div>
            
            <div class="hmm-component">
                <strong>🚀 Initial Probabilities (π):</strong> Probabilities of starting in particular states<br>
                Formula: $\pi_i = P(s_i \text{ at } t=1)$
            </div>

            <div class="visualization">
                <h4>HMM Structure Visualization</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Hidden States Layer -->
                    <text x="300" y="30" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Hidden States</text>
                    
                    <!-- State nodes -->
                    <circle cx="150" cy="80" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="150" y="85" font-size="12" fill="white" text-anchor="middle" font-weight="bold">Rainy</text>
                    
                    <circle cx="450" cy="80" r="25" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="450" y="85" font-size="12" fill="white" text-anchor="middle" font-weight="bold">Sunny</text>
                    
                    <!-- Transition arrows -->
                    <path d="M 175 80 Q 300 50 425 80" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <text x="300" y="65" font-size="10" fill="#e74c3c" text-anchor="middle">0.3</text>
                    
                    <path d="M 425 80 Q 300 110 175 80" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <text x="300" y="115" font-size="10" fill="#e74c3c" text-anchor="middle">0.4</text>
                    
                    <!-- Self loops -->
                    <path d="M 130 60 Q 110 40 130 100" stroke="#27ae60" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <text x="105" y="80" font-size="10" fill="#27ae60" text-anchor="middle">0.7</text>
                    
                    <path d="M 470 60 Q 490 40 470 100" stroke="#27ae60" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <text x="495" y="80" font-size="10" fill="#27ae60" text-anchor="middle">0.6</text>
                    
                    <!-- Observations Layer -->
                    <text x="300" y="180" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Observations</text>
                    
                    <!-- Observation nodes -->
                    <rect x="80" y="210" width="50" height="30" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="5"/>
                    <text x="105" y="230" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Walk</text>
                    
                    <rect x="275" y="210" width="50" height="30" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="5"/>
                    <text x="300" y="230" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Shop</text>
                    
                    <rect x="470" y="210" width="50" height="30" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="5"/>
                    <text x="495" y="230" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Clean</text>
                    
                    <!-- Emission arrows -->
                    <line x1="150" y1="105" x2="105" y2="210" stroke="#8e44ad" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="150" y1="105" x2="300" y2="210" stroke="#8e44ad" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="150" y1="105" x2="495" y2="210" stroke="#8e44ad" stroke-width="2" stroke-dasharray="5,5"/>
                    
                    <line x1="450" y1="105" x2="105" y2="210" stroke="#8e44ad" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="450" y1="105" x2="300" y2="210" stroke="#8e44ad" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="450" y1="105" x2="495" y2="210" stroke="#8e44ad" stroke-width="2" stroke-dasharray="5,5"/>
                    
                    <!-- Emission probabilities -->
                    <text x="125" y="160" font-size="9" fill="#8e44ad">0.1</text>
                    <text x="220" y="160" font-size="9" fill="#8e44ad">0.4</text>
                    <text x="320" y="160" font-size="9" fill="#8e44ad">0.5</text>
                    
                    <text x="280" y="160" font-size="9" fill="#8e44ad">0.6</text>
                    <text x="375" y="160" font-size="9" fill="#8e44ad">0.3</text>
                    <text x="470" y="160" font-size="9" fill="#8e44ad">0.1</text>
                    
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </section>

        <section id="algorithm" class="section">
            <h2>⚙️ The Viterbi Algorithm Overview</h2>

            <div class="highlight-box">
                <strong>The Decoding Problem:</strong> Given a sequence of observations, find the most likely sequence of hidden states that could have generated those observations.
            </div>

            <h3>What Does Viterbi Solve?</h3>
            <p>The Viterbi algorithm finds the optimal hidden path π that maximizes the probability P(x, π) for a given observation sequence x.</p>

            <div class="algorithm-step">
                <span class="step-counter">1</span>
                <strong>Initialization:</strong> Set up initial probabilities for the first time step
                <div class="formula-box">
                    $$V_1(j) = \pi_j \cdot b_j(o_1) \quad \forall j \in \{1, ..., N\}$$
                </div>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">2</span>
                <strong>Recursion:</strong> For each subsequent time step, find the most likely path
                <div class="formula-box">
                    $$V_t(j) = \max_i [V_{t-1}(i) \cdot a_{ij} \cdot b_j(o_t)]$$
                </div>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">3</span>
                <strong>Termination:</strong> Find the state with highest probability at the final time step
                <div class="formula-box">
                    $$P^* = \max_j V_T(j)$$
                </div>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">4</span>
                <strong>Backtracking:</strong> Trace back through the stored paths to find the optimal sequence
            </div>

            <div class="visualization">
                <h4>Viterbi Algorithm Flow</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Time steps -->
                    <text x="100" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=1</text>
                    <text x="250" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=2</text>
                    <text x="400" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=3</text>
                    <text x="550" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=4</text>

                    <!-- Observations -->
                    <rect x="75" y="350" width="50" height="25" fill="#9b59b6" rx="5"/>
                    <text x="100" y="367" font-size="11" fill="white" text-anchor="middle">Walk</text>

                    <rect x="225" y="350" width="50" height="25" fill="#9b59b6" rx="5"/>
                    <text x="250" y="367" font-size="11" fill="white" text-anchor="middle">Shop</text>

                    <rect x="375" y="350" width="50" height="25" fill="#9b59b6" rx="5"/>
                    <text x="400" y="367" font-size="11" fill="white" text-anchor="middle">Clean</text>

                    <rect x="525" y="350" width="50" height="25" fill="#9b59b6" rx="5"/>
                    <text x="550" y="367" font-size="11" fill="white" text-anchor="middle">Walk</text>

                    <!-- State nodes for each time step -->
                    <!-- t=1 -->
                    <circle cx="100" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="100" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="100" y="110" font-size="9" fill="#e74c3c" text-anchor="middle">0.06</text>

                    <circle cx="100" cy="150" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="100" y="155" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="100" y="180" font-size="9" fill="#e74c3c" text-anchor="middle">0.24</text>

                    <!-- t=2 -->
                    <circle cx="250" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="250" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="250" y="110" font-size="9" fill="#e74c3c" text-anchor="middle">0.0168</text>

                    <circle cx="250" cy="150" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="250" y="155" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="250" y="180" font-size="9" fill="#e74c3c" text-anchor="middle">0.0432</text>

                    <!-- t=3 -->
                    <circle cx="400" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="400" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="400" y="110" font-size="9" fill="#e74c3c" text-anchor="middle">0.0151</text>

                    <circle cx="400" cy="150" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="400" y="155" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="400" y="180" font-size="9" fill="#e74c3c" text-anchor="middle">0.0013</text>

                    <!-- t=4 -->
                    <circle cx="550" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="550" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="550" y="110" font-size="9" fill="#e74c3c" text-anchor="middle">0.0011</text>

                    <circle cx="550" cy="150" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="550" y="155" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="550" y="180" font-size="9" fill="#e74c3c" text-anchor="middle">0.0054</text>

                    <!-- Optimal path arrows -->
                    <path d="M 120 150 L 230 80" stroke="#27ae60" stroke-width="4" fill="none" marker-end="url(#greenArrow)"/>
                    <path d="M 270 80 L 380 80" stroke="#27ae60" stroke-width="4" fill="none" marker-end="url(#greenArrow)"/>
                    <path d="M 420 80 L 530 150" stroke="#27ae60" stroke-width="4" fill="none" marker-end="url(#greenArrow)"/>

                    <!-- Other possible paths (lighter) -->
                    <path d="M 120 80 L 230 80" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                    <path d="M 120 80 L 230 150" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                    <path d="M 120 150 L 230 150" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>

                    <path d="M 270 80 L 380 150" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                    <path d="M 270 150 L 380 80" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                    <path d="M 270 150 L 380 150" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>

                    <path d="M 420 80 L 530 80" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                    <path d="M 420 150 L 530 80" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                    <path d="M 420 150 L 530 150" stroke="#bdc3c7" stroke-width="2" fill="none" stroke-dasharray="3,3"/>

                    <!-- Legend -->
                    <text x="350" y="250" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">Optimal Path: Sunny → Rainy → Rainy → Sunny</text>
                    <line x1="250" y1="270" x2="280" y2="270" stroke="#27ae60" stroke-width="4"/>
                    <text x="290" y="275" font-size="12" fill="#27ae60">Most Likely Path</text>

                    <line x1="250" y1="290" x2="280" y2="290" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="3,3"/>
                    <text x="290" y="295" font-size="12" fill="#95a5a6">Other Possible Paths</text>

                    <defs>
                        <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="highlight-box">
                <strong>Key Insight:</strong> The Viterbi algorithm uses dynamic programming to efficiently find the most probable sequence by building up solutions from smaller subproblems. At each step, it only keeps track of the best path to each state, dramatically reducing computational complexity.
            </div>
        </section>

        <section id="mathematics" class="section">
            <h2>📐 Mathematical Foundation</h2>

            <p>The Viterbi algorithm is built on solid mathematical foundations. Let's explore the key formulas and their meanings:</p>

            <h3>1. Initialization Step</h3>
            <p>For the first observation, we calculate the probability of being in each state:</p>

            <div class="formula-box">
                $$V_1(j) = \pi_j \cdot b_j(o_1) \quad \forall j \in \{1, ..., N\}$$
            </div>

            <div class="algorithm-step">
                <strong>Breaking it down:</strong><br>
                • $V_1(j)$: Probability of being in state $j$ at time 1<br>
                • $\pi_j$: Initial probability of state $j$<br>
                • $b_j(o_1)$: Probability of observing $o_1$ from state $j$<br>
                • We also initialize the path: $Path_j(1) = [j]$
            </div>

            <h3>2. Recursion Step</h3>
            <p>For each subsequent time step, we find the most likely path to each state:</p>

            <div class="formula-box">
                $$V_t(j) = \max_i [V_{t-1}(i) \cdot a_{ij} \cdot b_j(o_t)]$$
            </div>

            <div class="formula-box">
                $$Path_j(t) = [\arg\max_i [V_{t-1}(i) \cdot a_{ij}], j]$$
            </div>

            <div class="algorithm-step">
                <strong>Understanding the recursion:</strong><br>
                • $V_t(j)$: Maximum probability of reaching state $j$ at time $t$<br>
                • $V_{t-1}(i)$: Best probability of being in state $i$ at time $t-1$<br>
                • $a_{ij}$: Transition probability from state $i$ to state $j$<br>
                • $b_j(o_t)$: Emission probability of observation $o_t$ from state $j$<br>
                • We take the maximum over all possible previous states $i$
            </div>

            <h3>3. Termination Step</h3>
            <p>Finally, we find the best overall path and its probability:</p>

            <div class="formula-box">
                $$P^* = \max_j V_T(j)$$
            </div>

            <div class="formula-box">
                $$\text{Best Path} = \arg\max_j V_T(j)$$
            </div>

            <div class="visualization">
                <h4>Mathematical Visualization: Recursion in Action</h4>
                <svg width="600" height="350" viewBox="0 0 600 350">
                    <!-- Time step labels -->
                    <text x="150" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t-1</text>
                    <text x="450" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t</text>

                    <!-- States at t-1 -->
                    <circle cx="150" cy="80" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="150" y="85" font-size="12" fill="white" text-anchor="middle">S₁</text>
                    <text x="150" y="115" font-size="10" fill="#2c3e50" text-anchor="middle">V_{t-1}(1) = 0.3</text>

                    <circle cx="150" cy="180" r="25" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                    <text x="150" y="185" font-size="12" fill="white" text-anchor="middle">S₂</text>
                    <text x="150" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">V_{t-1}(2) = 0.5</text>

                    <circle cx="150" cy="280" r="25" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="150" y="285" font-size="12" fill="white" text-anchor="middle">S₃</text>
                    <text x="150" y="315" font-size="10" fill="#2c3e50" text-anchor="middle">V_{t-1}(3) = 0.2</text>

                    <!-- Target state at t -->
                    <circle cx="450" cy="180" r="25" fill="#27ae60" stroke="#2c3e50" stroke-width="3"/>
                    <text x="450" y="185" font-size="12" fill="white" text-anchor="middle">Sⱼ</text>
                    <text x="450" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">V_t(j) = ?</text>

                    <!-- Transition arrows with probabilities -->
                    <path d="M 175 80 L 425 180" stroke="#8e44ad" stroke-width="2" fill="none" marker-end="url(#purpleArrow)"/>
                    <text x="280" y="120" font-size="10" fill="#8e44ad" text-anchor="middle">a₁ⱼ = 0.4</text>

                    <path d="M 175 180 L 425 180" stroke="#8e44ad" stroke-width="3" fill="none" marker-end="url(#purpleArrow)"/>
                    <text x="300" y="170" font-size="10" fill="#8e44ad" text-anchor="middle">a₂ⱼ = 0.6</text>

                    <path d="M 175 280 L 425 180" stroke="#8e44ad" stroke-width="2" fill="none" marker-end="url(#purpleArrow)"/>
                    <text x="320" y="240" font-size="10" fill="#8e44ad" text-anchor="middle">a₃ⱼ = 0.3</text>

                    <!-- Calculation box -->
                    <rect x="50" y="50" width="200" height="120" fill="#f8f9fa" stroke="#ddd" stroke-width="1" rx="5"/>
                    <text x="150" y="70" font-size="12" fill="#2c3e50" text-anchor="middle" font-weight="bold">Calculations:</text>
                    <text x="60" y="90" font-size="10" fill="#2c3e50">Path 1: 0.3 × 0.4 × b_j(o_t) = 0.12 × b_j(o_t)</text>
                    <text x="60" y="110" font-size="10" fill="#e74c3c" font-weight="bold">Path 2: 0.5 × 0.6 × b_j(o_t) = 0.30 × b_j(o_t)</text>
                    <text x="60" y="130" font-size="10" fill="#2c3e50">Path 3: 0.2 × 0.3 × b_j(o_t) = 0.06 × b_j(o_t)</text>
                    <text x="60" y="150" font-size="10" fill="#27ae60" font-weight="bold">MAX = Path 2 (from S₂)</text>

                    <!-- Emission probability -->
                    <text x="450" y="250" font-size="12" fill="#9b59b6" text-anchor="middle">× b_j(o_t)</text>
                    <rect x="420" y="270" width="60" height="25" fill="#9b59b6" rx="5"/>
                    <text x="450" y="287" font-size="11" fill="white" text-anchor="middle">o_t</text>

                    <defs>
                        <marker id="purpleArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#8e44ad"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>4. Comparison with Forward Algorithm</h3>
            <div class="highlight-box">
                <strong>Key Difference:</strong><br>
                • <strong>Forward Algorithm:</strong> Sums probabilities: $\alpha_t(j) = \sum_i [\alpha_{t-1}(i) \cdot a_{ij} \cdot b_j(o_t)]$<br>
                • <strong>Viterbi Algorithm:</strong> Takes maximum: $V_t(j) = \max_i [V_{t-1}(i) \cdot a_{ij} \cdot b_j(o_t)]$<br><br>
                Forward finds the total probability of all paths, while Viterbi finds the single best path.
            </div>

            <h3>5. Logarithmic Implementation</h3>
            <p>To avoid numerical underflow, we often use logarithms:</p>

            <div class="formula-box">
                $$\log V_t(j) = \max_i [\log V_{t-1}(i) + \log a_{ij} + \log b_j(o_t)]$$
            </div>

            <div class="algorithm-step">
                <strong>Benefits of log space:</strong><br>
                • Prevents underflow for very small probabilities<br>
                • Converts multiplications to additions<br>
                • Maintains numerical stability<br>
                • Essential for long sequences
            </div>
        </section>

        <section id="graph" class="section">
            <h2>🕸️ The Viterbi Graph</h2>

            <div class="highlight-box">
                <strong>Graph Representation:</strong> The Viterbi algorithm can be visualized as finding the path with maximum product weight in a directed acyclic graph (DAG).
            </div>

            <h3>Graph Structure</h3>
            <p>The Viterbi graph is organized as a grid with:</p>

            <div class="algorithm-step">
                <strong>Dimensions:</strong><br>
                • <strong>Rows:</strong> |States| rows (one for each hidden state)<br>
                • <strong>Columns:</strong> n columns (one for each observation)<br>
                • <strong>Nodes:</strong> Each node (k,i) represents state k at time i
            </div>

            <div class="algorithm-step">
                <strong>Edges:</strong><br>
                • Every node in column i-1 connects to every node in column i<br>
                • Edge weight from (l,i-1) to (k,i): $Weight_i(l,k) = a_{lk} \cdot b_k(o_i)$<br>
                • Additional source and sink nodes for initialization and termination
            </div>

            <div class="visualization">
                <h4>Viterbi Graph Structure</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Column labels -->
                    <text x="100" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">Source</text>
                    <text x="200" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=1</text>
                    <text x="300" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=2</text>
                    <text x="400" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=3</text>
                    <text x="500" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=4</text>
                    <text x="600" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">Sink</text>

                    <!-- Observations -->
                    <rect x="175" y="350" width="50" height="20" fill="#9b59b6" rx="3"/>
                    <text x="200" y="363" font-size="10" fill="white" text-anchor="middle">H</text>

                    <rect x="275" y="350" width="50" height="20" fill="#9b59b6" rx="3"/>
                    <text x="300" y="363" font-size="10" fill="white" text-anchor="middle">H</text>

                    <rect x="375" y="350" width="50" height="20" fill="#9b59b6" rx="3"/>
                    <text x="400" y="363" font-size="10" fill="white" text-anchor="middle">T</text>

                    <rect x="475" y="350" width="50" height="20" fill="#9b59b6" rx="3"/>
                    <text x="500" y="363" font-size="10" fill="white" text-anchor="middle">T</text>

                    <!-- Source node -->
                    <circle cx="100" cy="200" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="100" y="205" font-size="10" fill="white" text-anchor="middle">SRC</text>

                    <!-- Sink node -->
                    <circle cx="600" cy="200" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                    <text x="600" y="205" font-size="10" fill="white" text-anchor="middle">SNK</text>

                    <!-- State nodes for each time step -->
                    <!-- t=1 -->
                    <circle cx="200" cy="120" r="12" fill="#27ae60" stroke="#2c3e50" stroke-width="1"/>
                    <text x="200" y="125" font-size="9" fill="white" text-anchor="middle">F</text>

                    <circle cx="200" cy="280" r="12" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                    <text x="200" y="285" font-size="9" fill="white" text-anchor="middle">B</text>

                    <!-- t=2 -->
                    <circle cx="300" cy="120" r="12" fill="#27ae60" stroke="#2c3e50" stroke-width="1"/>
                    <text x="300" y="125" font-size="9" fill="white" text-anchor="middle">F</text>

                    <circle cx="300" cy="280" r="12" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                    <text x="300" y="285" font-size="9" fill="white" text-anchor="middle">B</text>

                    <!-- t=3 -->
                    <circle cx="400" cy="120" r="12" fill="#27ae60" stroke="#2c3e50" stroke-width="1"/>
                    <text x="400" y="125" font-size="9" fill="white" text-anchor="middle">F</text>

                    <circle cx="400" cy="280" r="12" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                    <text x="400" y="285" font-size="9" fill="white" text-anchor="middle">B</text>

                    <!-- t=4 -->
                    <circle cx="500" cy="120" r="12" fill="#27ae60" stroke="#2c3e50" stroke-width="1"/>
                    <text x="500" y="125" font-size="9" fill="white" text-anchor="middle">F</text>

                    <circle cx="500" cy="280" r="12" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                    <text x="500" y="285" font-size="9" fill="white" text-anchor="middle">B</text>

                    <!-- Source to t=1 edges -->
                    <line x1="115" y1="200" x2="188" y2="120" stroke="#8e44ad" stroke-width="2"/>
                    <line x1="115" y1="200" x2="188" y2="280" stroke="#8e44ad" stroke-width="2"/>

                    <!-- t=1 to t=2 edges (showing all connections) -->
                    <line x1="212" y1="120" x2="288" y2="120" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="212" y1="120" x2="288" y2="280" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="212" y1="280" x2="288" y2="120" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="212" y1="280" x2="288" y2="280" stroke="#95a5a6" stroke-width="1"/>

                    <!-- t=2 to t=3 edges -->
                    <line x1="312" y1="120" x2="388" y2="120" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="312" y1="120" x2="388" y2="280" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="312" y1="280" x2="388" y2="120" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="312" y1="280" x2="388" y2="280" stroke="#95a5a6" stroke-width="1"/>

                    <!-- t=3 to t=4 edges -->
                    <line x1="412" y1="120" x2="488" y2="120" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="412" y1="120" x2="488" y2="280" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="412" y1="280" x2="488" y2="120" stroke="#95a5a6" stroke-width="1"/>
                    <line x1="412" y1="280" x2="488" y2="280" stroke="#95a5a6" stroke-width="1"/>

                    <!-- t=4 to sink edges -->
                    <line x1="512" y1="120" x2="585" y2="200" stroke="#8e44ad" stroke-width="2"/>
                    <line x1="512" y1="280" x2="585" y2="200" stroke="#8e44ad" stroke-width="2"/>

                    <!-- Optimal path highlighting -->
                    <path d="M 115 200 L 188 280 L 288 280 L 388 280 L 488 120 L 585 200"
                          stroke="#e74c3c" stroke-width="4" fill="none" opacity="0.8"/>

                    <!-- Edge weight examples -->
                    <text x="150" y="240" font-size="8" fill="#8e44ad" text-anchor="middle">π₁·b₁(H)</text>
                    <text x="150" y="160" font-size="8" fill="#8e44ad" text-anchor="middle">π₂·b₂(H)</text>

                    <text x="250" y="200" font-size="8" fill="#95a5a6" text-anchor="middle">a₁₂·b₂(H)</text>

                    <!-- Legend -->
                    <text x="350" y="50" font-size="12" fill="#2c3e50" text-anchor="middle" font-weight="bold">Crooked Dealer HMM: "HHTT"</text>
                    <line x1="250" y1="70" x2="280" y2="70" stroke="#e74c3c" stroke-width="4"/>
                    <text x="290" y="75" font-size="10" fill="#e74c3c">Optimal Path: BBBBF</text>

                    <circle cx="260" cy="90" r="8" fill="#27ae60"/>
                    <text x="275" y="95" font-size="10" fill="#2c3e50">Fair Coin (F)</text>

                    <circle cx="360" cy="90" r="8" fill="#f39c12"/>
                    <text x="375" y="95" font-size="10" fill="#2c3e50">Biased Coin (B)</text>
                </svg>
            </div>

            <h3>Edge Weights and Path Interpretation</h3>

            <div class="algorithm-step">
                <strong>Edge Weight Formula:</strong><br>
                For edge from node (l, i-1) to node (k, i):
                <div class="formula-box">
                    $$Weight_i(l,k) = a_{lk} \cdot b_k(o_i)$$
                </div>
            </div>

            <div class="algorithm-step">
                <strong>Path Weight:</strong><br>
                The product weight of a complete path equals:
                <div class="formula-box">
                    $$\prod_{i=1}^n Weight_i(\pi_{i-1}, \pi_i) = P(x, \pi)$$
                </div>
            </div>

            <h3>Graph Properties</h3>

            <div class="highlight-box">
                <strong>Complexity Analysis:</strong><br>
                • <strong>Nodes:</strong> |States| × n + 2 (including source and sink)<br>
                • <strong>Edges:</strong> |States|² × (n-1) + 2×|States|<br>
                • <strong>Time Complexity:</strong> O(|States|² × n)<br>
                • <strong>Space Complexity:</strong> O(|States| × n)
            </div>

            <div class="algorithm-step">
                <strong>Optimization Techniques:</strong><br>
                • <strong>Pruning:</strong> Remove low-probability paths early<br>
                • <strong>Beam Search:</strong> Keep only top-k paths at each step<br>
                • <strong>Sparse Transitions:</strong> Skip forbidden state transitions<br>
                • <strong>Log Space:</strong> Use logarithms to prevent underflow
            </div>
        </section>

        <section id="example" class="section">
            <h2>🔍 Step-by-Step Example: Weather Prediction</h2>

            <p>Let's work through a complete example using the weather HMM from our source material.</p>

            <h3>Problem Setup</h3>
            <div class="highlight-box">
                <strong>Scenario:</strong> Predict weather states (Rainy, Sunny) based on observed activities (Walk, Shop, Clean)<br>
                <strong>Observation Sequence:</strong> [Walk, Shop, Clean]<br>
                <strong>Goal:</strong> Find the most likely weather sequence
            </div>

            <h3>HMM Parameters</h3>

            <div class="matrix">
                <h4>Transition Matrix (A)</h4>
                <table>
                    <tr><th></th><th>Rainy</th><th>Sunny</th></tr>
                    <tr><th>Rainy</th><td>0.7</td><td>0.3</td></tr>
                    <tr><th>Sunny</th><td>0.4</td><td>0.6</td></tr>
                </table>
            </div>

            <div class="matrix">
                <h4>Emission Matrix (B)</h4>
                <table>
                    <tr><th></th><th>Walk</th><th>Shop</th><th>Clean</th></tr>
                    <tr><th>Rainy</th><td>0.1</td><td>0.4</td><td>0.5</td></tr>
                    <tr><th>Sunny</th><td>0.6</td><td>0.3</td><td>0.1</td></tr>
                </table>
            </div>

            <div class="matrix">
                <h4>Initial Probabilities (π)</h4>
                <table>
                    <tr><th>Rainy</th><th>Sunny</th></tr>
                    <tr><td>0.6</td><td>0.4</td></tr>
                </table>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">1</span>
                <strong>Step 1: Initialization (t=1, observation="Walk")</strong>

                <div class="formula-box">
                    $$V_1(\text{Rainy}) = \pi_{\text{Rainy}} \cdot b_{\text{Rainy}}(\text{Walk}) = 0.6 \times 0.1 = 0.06$$
                </div>

                <div class="formula-box">
                    $$V_1(\text{Sunny}) = \pi_{\text{Sunny}} \cdot b_{\text{Sunny}}(\text{Walk}) = 0.4 \times 0.6 = 0.24$$
                </div>

                <p><strong>Paths:</strong> Path_Rainy(1) = [Rainy], Path_Sunny(1) = [Sunny]</p>
            </div>

            <button class="interactive-button" onclick="showStep2()">Continue to Step 2 →</button>

            <div id="step2" style="display: none;">
                <div class="algorithm-step">
                    <span class="step-counter">2</span>
                    <strong>Step 2: Recursion (t=2, observation="Shop")</strong>

                    <p>For Rainy state at t=2:</p>
                    <div class="formula-box">
                        $$V_2(\text{Rainy}) = \max \begin{cases}
                        V_1(\text{Rainy}) \times a_{\text{Rainy,Rainy}} \times b_{\text{Rainy}}(\text{Shop}) = 0.06 \times 0.7 \times 0.4 = 0.0168 \\
                        V_1(\text{Sunny}) \times a_{\text{Sunny,Rainy}} \times b_{\text{Rainy}}(\text{Shop}) = 0.24 \times 0.4 \times 0.4 = 0.0384
                        \end{cases}$$
                    </div>

                    <p>Maximum is 0.0384 (from Sunny), so Path_Rainy(2) = [Sunny, Rainy]</p>

                    <p>For Sunny state at t=2:</p>
                    <div class="formula-box">
                        $$V_2(\text{Sunny}) = \max \begin{cases}
                        V_1(\text{Rainy}) \times a_{\text{Rainy,Sunny}} \times b_{\text{Sunny}}(\text{Shop}) = 0.06 \times 0.3 \times 0.3 = 0.0054 \\
                        V_1(\text{Sunny}) \times a_{\text{Sunny,Sunny}} \times b_{\text{Sunny}}(\text{Shop}) = 0.24 \times 0.6 \times 0.3 = 0.0432
                        \end{cases}$$
                    </div>

                    <p>Maximum is 0.0432 (from Sunny), so Path_Sunny(2) = [Sunny, Sunny]</p>
                </div>
            </div>

            <button class="interactive-button" onclick="showStep3()" style="display: none;" id="step3Button">Continue to Step 3 →</button>

            <div id="step3" style="display: none;">
                <div class="algorithm-step">
                    <span class="step-counter">3</span>
                    <strong>Step 3: Final Recursion (t=3, observation="Clean")</strong>

                    <p>For Rainy state at t=3:</p>
                    <div class="formula-box">
                        $$V_3(\text{Rainy}) = \max \begin{cases}
                        0.0384 \times 0.7 \times 0.5 = 0.01344 \\
                        0.0432 \times 0.4 \times 0.5 = 0.00864
                        \end{cases} = 0.01344$$
                    </div>

                    <p>Path_Rainy(3) = [Sunny, Rainy, Rainy]</p>

                    <p>For Sunny state at t=3:</p>
                    <div class="formula-box">
                        $$V_3(\text{Sunny}) = \max \begin{cases}
                        0.0384 \times 0.3 \times 0.1 = 0.001152 \\
                        0.0432 \times 0.6 \times 0.1 = 0.002592
                        \end{cases} = 0.002592$$
                    </div>

                    <p>Path_Sunny(3) = [Sunny, Sunny, Sunny]</p>
                </div>

                <div class="algorithm-step">
                    <span class="step-counter">4</span>
                    <strong>Step 4: Termination</strong>

                    <div class="formula-box">
                        $$P^* = \max(V_3(\text{Rainy}), V_3(\text{Sunny})) = \max(0.01344, 0.002592) = 0.01344$$
                    </div>

                    <div class="highlight-box">
                        <strong>🎉 Result:</strong><br>
                        <strong>Most Probable Path:</strong> [Sunny, Rainy, Rainy]<br>
                        <strong>Probability:</strong> 0.01344<br><br>
                        <strong>Interpretation:</strong> Given the observations [Walk, Shop, Clean], the most likely weather sequence is Sunny → Rainy → Rainy.
                    </div>
                </div>
            </div>

            <div class="visualization">
                <h4>Complete Viterbi Trellis</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Time labels -->
                    <text x="150" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=1 (Walk)</text>
                    <text x="300" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=2 (Shop)</text>
                    <text x="450" y="30" font-size="14" fill="#2c3e50" text-anchor="middle">t=3 (Clean)</text>

                    <!-- State nodes with probabilities -->
                    <!-- t=1 -->
                    <circle cx="150" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="150" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="150" y="110" font-size="9" fill="#2c3e50" text-anchor="middle">0.06</text>

                    <circle cx="150" cy="180" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="150" y="185" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="150" y="210" font-size="9" fill="#2c3e50" text-anchor="middle">0.24</text>

                    <!-- t=2 -->
                    <circle cx="300" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="300" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="300" y="110" font-size="9" fill="#2c3e50" text-anchor="middle">0.0384</text>

                    <circle cx="300" cy="180" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="300" y="185" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="300" y="210" font-size="9" fill="#2c3e50" text-anchor="middle">0.0432</text>

                    <!-- t=3 -->
                    <circle cx="450" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                    <text x="450" y="85" font-size="10" fill="white" text-anchor="middle">R</text>
                    <text x="450" y="110" font-size="9" fill="#e74c3c" text-anchor="middle" font-weight="bold">0.01344</text>

                    <circle cx="450" cy="180" r="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                    <text x="450" y="185" font-size="10" fill="white" text-anchor="middle">S</text>
                    <text x="450" y="210" font-size="9" fill="#2c3e50" text-anchor="middle">0.00259</text>

                    <!-- Optimal path -->
                    <path d="M 170 180 L 280 80 L 430 80" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#redArrow)"/>

                    <!-- All possible transitions (lighter) -->
                    <line x1="170" y1="80" x2="280" y2="80" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="3,3"/>
                    <line x1="170" y1="80" x2="280" y2="180" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="3,3"/>
                    <line x1="170" y1="180" x2="280" y2="180" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="3,3"/>

                    <line x1="320" y1="80" x2="430" y2="80" stroke="#e74c3c" stroke-width="2"/>
                    <line x1="320" y1="80" x2="430" y2="180" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="3,3"/>
                    <line x1="320" y1="180" x2="430" y2="80" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="3,3"/>
                    <line x1="320" y1="180" x2="430" y2="180" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="3,3"/>

                    <!-- Legend -->
                    <text x="300" y="260" font-size="14" fill="#e74c3c" text-anchor="middle" font-weight="bold">Optimal Path: Sunny → Rainy → Rainy</text>

                    <defs>
                        <marker id="redArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </section>

        <section id="implementation" class="section">
            <h2>💻 Implementation and Applications</h2>

            <h3>Python Implementation</h3>
            <p>Here's a complete implementation of the Viterbi algorithm:</p>

            <div class="code-block">
import numpy as np

def viterbi(observations, states, start_prob, trans_prob, emit_prob):
    """
    Viterbi algorithm for finding the most likely sequence of hidden states

    Args:
        observations: sequence of observed events
        states: list of possible hidden states
        start_prob: initial state probabilities
        trans_prob: transition probabilities between states
        emit_prob: emission probabilities for observations given states

    Returns:
        (probability, path): most likely probability and state sequence
    """
    V = [{}]  # Viterbi probabilities
    path = {}  # Best paths

    # Initialize base cases (t=0)
    for state in states:
        V[0][state] = start_prob[state] * emit_prob[state][observations[0]]
        path[state] = [state]

    # Run Viterbi for t > 0
    for t in range(1, len(observations)):
        V.append({})
        newpath = {}

        for curr_state in states:
            # Find the most likely previous state
            (prob, prev_state) = max(
                [(V[t-1][prev_state] * trans_prob[prev_state][curr_state] *
                  emit_prob[curr_state][observations[t]], prev_state)
                 for prev_state in states]
            )

            V[t][curr_state] = prob
            newpath[curr_state] = path[prev_state] + [curr_state]

        path = newpath

    # Find the most likely final state
    (prob, state) = max([(V[-1][state], state) for state in states])
    return (prob, path[state])

# Example usage with weather data
states = ('Rainy', 'Sunny')
observations = ('Walk', 'Shop', 'Clean')

start_probability = {'Rainy': 0.6, 'Sunny': 0.4}

transition_probability = {
    'Rainy': {'Rainy': 0.7, 'Sunny': 0.3},
    'Sunny': {'Rainy': 0.4, 'Sunny': 0.6},
}

emission_probability = {
    'Rainy': {'Walk': 0.1, 'Shop': 0.4, 'Clean': 0.5},
    'Sunny': {'Walk': 0.6, 'Shop': 0.3, 'Clean': 0.1},
}

prob, path = viterbi(observations, states, start_probability,
                    transition_probability, emission_probability)

print(f"Most likely path: {path}")
print(f"Probability: {prob:.6f}")
            </div>

            <h3>Real-World Applications</h3>

            <div class="algorithm-step">
                <strong>🗣️ Speech Recognition</strong><br>
                • Hidden states: phonemes or words<br>
                • Observations: acoustic features (MFCCs, spectrograms)<br>
                • Goal: Convert speech signals to text
            </div>

            <div class="algorithm-step">
                <strong>🧬 Bioinformatics</strong><br>
                • Hidden states: gene regions (exon, intron, promoter)<br>
                • Observations: DNA nucleotide sequences<br>
                • Goal: Gene finding and sequence annotation
            </div>

            <div class="algorithm-step">
                <strong>📝 Natural Language Processing</strong><br>
                • Hidden states: part-of-speech tags<br>
                • Observations: words in sentences<br>
                • Goal: Grammatical analysis and parsing
            </div>

            <div class="algorithm-step">
                <strong>📡 Digital Communications</strong><br>
                • Hidden states: transmitted symbols<br>
                • Observations: received noisy signals<br>
                • Goal: Error correction and signal decoding
            </div>

            <h3>Performance Analysis</h3>

            <div class="visualization">
                <h4>Complexity Comparison</h4>
                <svg width="600" height="250" viewBox="0 0 600 250">
                    <!-- Bars for different algorithms -->
                    <rect x="50" y="180" width="80" height="50" fill="#27ae60"/>
                    <text x="90" y="210" font-size="12" fill="white" text-anchor="middle">Viterbi</text>
                    <text x="90" y="245" font-size="10" fill="#2c3e50" text-anchor="middle">O(N²T)</text>

                    <rect x="150" y="120" width="80" height="110" fill="#f39c12"/>
                    <text x="190" y="180" font-size="12" fill="white" text-anchor="middle">Brute Force</text>
                    <text x="190" y="245" font-size="10" fill="#2c3e50" text-anchor="middle">O(N^T)</text>

                    <rect x="250" y="160" width="80" height="70" fill="#3498db"/>
                    <text x="290" y="200" font-size="12" fill="white" text-anchor="middle">Forward</text>
                    <text x="290" y="245" font-size="10" fill="#2c3e50" text-anchor="middle">O(N²T)</text>

                    <rect x="350" y="170" width="80" height="60" fill="#9b59b6"/>
                    <text x="390" y="205" font-size="12" fill="white" text-anchor="middle">Backward</text>
                    <text x="390" y="245" font-size="10" fill="#2c3e50" text-anchor="middle">O(N²T)</text>

                    <!-- Y-axis -->
                    <line x1="40" y1="50" x2="40" y2="230" stroke="#2c3e50" stroke-width="2"/>
                    <text x="25" y="140" font-size="12" fill="#2c3e50" text-anchor="middle" transform="rotate(-90 25 140)">Time Complexity</text>

                    <!-- Title -->
                    <text x="300" y="30" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Algorithm Complexity Comparison</text>

                    <!-- Note -->
                    <text x="300" y="270" font-size="10" fill="#7f8c8d" text-anchor="middle">N = number of states, T = sequence length</text>
                </svg>
            </div>

            <div class="highlight-box">
                <strong>Key Advantages of Viterbi:</strong><br>
                • <strong>Optimal:</strong> Guaranteed to find the most likely path<br>
                • <strong>Efficient:</strong> Polynomial time complexity O(N²T)<br>
                • <strong>Memory Efficient:</strong> Only needs to store previous time step<br>
                • <strong>Parallelizable:</strong> State computations can be done in parallel<br>
                • <strong>Robust:</strong> Works well with log probabilities to prevent underflow
            </div>

            <h3>Extensions and Variants</h3>

            <div class="algorithm-step">
                <strong>🔍 Beam Search Viterbi</strong><br>
                Keep only the top-K most likely paths at each time step to reduce memory and computation.
            </div>

            <div class="algorithm-step">
                <strong>📊 Log-Space Viterbi</strong><br>
                Use logarithms to prevent numerical underflow for long sequences.
            </div>

            <div class="algorithm-step">
                <strong>🌐 Parallel Viterbi</strong><br>
                Distribute state computations across multiple processors for faster execution.
            </div>

            <div class="algorithm-step">
                <strong>🎯 Constrained Viterbi</strong><br>
                Add constraints to force certain state transitions or sequences.
            </div>

            <div class="highlight-box">
                <strong>🎓 Conclusion</strong><br>
                The Viterbi algorithm is a fundamental tool in sequence analysis that efficiently finds the most likely hidden state sequence in HMMs. Its applications span from speech recognition to bioinformatics, making it one of the most important algorithms in computational analysis. The key insight is using dynamic programming to avoid exponential complexity while maintaining optimality.
            </div>
        </section>
    </div>

    <script>
        // Add smooth scrolling for navigation
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Interactive example functions
        function showStep2() {
            document.getElementById('step2').style.display = 'block';
            document.getElementById('step3Button').style.display = 'inline-block';
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([document.getElementById('step2')]).catch((err) => console.log(err.message));
            }
        }

        function showStep3() {
            document.getElementById('step3').style.display = 'block';
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([document.getElementById('step3')]).catch((err) => console.log(err.message));
            }
        }

        // Add interactive highlighting for matrices and tables
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to table cells
            const tableCells = document.querySelectorAll('td');
            tableCells.forEach(cell => {
                cell.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.2s';
                    this.style.backgroundColor = '#e8f4fd';
                });

                cell.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.backgroundColor = '';
                });
            });

            // Add progress indicator for navigation
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');

            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });

            // Add animation to step counters
            const stepCounters = document.querySelectorAll('.step-counter');
            stepCounters.forEach(counter => {
                counter.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(360deg)';
                    this.style.transition = 'transform 0.5s';
                });

                counter.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });

            // Add interactive code block highlighting
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.addEventListener('click', function() {
                    // Select all text in the code block
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Show a temporary message
                    const message = document.createElement('div');
                    message.textContent = 'Code copied to clipboard!';
                    message.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        background: #27ae60;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 5px;
                        z-index: 1000;
                        font-size: 14px;
                    `;
                    document.body.appendChild(message);

                    setTimeout(() => {
                        document.body.removeChild(message);
                    }, 2000);
                });
            });
        });

        // Add CSS for active navigation and animations
        const style = document.createElement('style');
        style.textContent = `
            .navigation a.active {
                color: #ff6b6b !important;
                font-weight: bold;
            }

            .navigation a.active::before {
                content: "→ ";
                color: #ff6b6b;
            }

            .algorithm-step:hover {
                transform: translateX(5px);
                transition: transform 0.3s ease;
            }

            .formula-box:hover {
                box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block:hover {
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block::after {
                content: "Click to select all";
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.1);
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .code-block:hover::after {
                opacity: 1;
            }

            .code-block {
                position: relative;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
