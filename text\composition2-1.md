Chapter 2
 Fundamental Concepts of Compositional Data
 Analysis
 Abstract Compositional data is considered a statistical scale in its own right,
 with its own natural geometry and its own vector space structure. Compositional
 data analysis and this book cannot be understood without a basic knowledge of
 these issues and how they are represented in R. Therefore, this chapter intro
duces the basic geometric concepts of compositional data and of the R-package
 “compositions”: the relative nature of compositional data; how to load, repre
sent, and display compositional data in R; the various compositional scales and
 geometries and how to select the right geometry for the problem at hand; and
 how to specify the geometry in “compositions”using the basic classes “acomp”,
 “rcomp”, “aplus”, “rplus”, “ccomp”, and “rplus”. A concise guide to the
 most important geometry for compositional data, the Aitchison geometry, is also
 included. The whole book relies on these basic principles, and the reader should
 make him or herself familiar with them in Sects.2.1–2.5 before going on.
 2.1 APractical View to Compositional Concepts
 2.1.1 Definition of Compositional Data
 A composition is often defined as a vector of D positive components x D
 Œx1;:::;xD summing up to a given constant , set typically equal to 1 (portions),
 100 (percentages), or 106 (ppm). We already mentioned that this definition is
 misleading, because many compositional datasets do not apparently satisfy it. Let
 us see how.
 Consider, for instance, this simple example of compositional data collected from
 typical nutrition tables of foodstuff in any kitchen cupboard:
 Fat
 soy
 peas
 7
 3
 Sodium Carbonates Protein Total
 14
 12
 10
 61
 13
 22
 100
 100
 K.G. van den Boogaart and R. Tolosana-Delgado, Analyzing Compositional Data with R,
 Use R!, DOI 10.1007/978-3-642-36809-7 2, © Springer-Verlag Berlin Heidelberg 2013
 13
14 2 FundamentalConceptsofCompositionalDataAnalysis
 wheat 1 15 60 23 100
 corn 1 180 15 2 82
 beans 0.5 680 30 12 NA
 Thisdatacanbeloadedfromatextfile1 intoRwiththecommandread.table:
 >library(compositions)
 >kitchen<-read.table("SimpleData.txt",sep="")
 All theamountsaregiven ingramsg, except for sodiumwhich isgiven in
 milligramsmg.Anaive interpretationof these rawnumberswould tell us that
 soyandcornarepoor inproteinandcarbonates,whereasbeanshaveseemingly
 4timesmoresodiumandtwicemorecarbonates thancorn.However, thisnaive
 (i.e.,non-compositional)directinterpretationcontradictseverythingweknowabout
 soyorcorn.Foragoodcompositionalanalysis, it is important tounderstandthe
 fundamentaloperationsandlawsgoverningcompositionalanalysistoavoidthese
 pitfalls.Thischapterintroducesthismethodologicalbackgroundstepbystep.
 2.1.2 SubcompositionsandtheClosureOperation
 Afirst inspectionshows that theentriesdonot sumuptothe total100%, since
 theseproductsdonotexclusivelycontainthenutrientsreported.Wethusonlyget
 somepartsof abigger composition,whichwould includewater, vitamins, and
 furtherminerals.Forinstance,thesenon-reportedcomponentsrepresent100 .7C
 14=1000C10C13/ 70goutof100gofsoy.
 Aspeoplewithacholesterolproblemandanofficejob,wemaybeinterestedin
 gettingthemostproteinfortheleastamountoffatwhilemaintainingareasonable
 mediumamount of carbohydrates.Ourwater andsodiumintake ismuchmore
 influencedbydrinkingandsaltingthanbyadietchoicebetweenbeansandpeas.
 Thus,we are going to focus our attention to the compositionof fat–protein
carbohydrates.It istypical toclosetheamountsinthepartsof interest tosumup
 to100%. In“compositions,”thiscanbedonewiththecommandclo(forclose
 composition):
 >(coi<-clo(kitchen,parts=
 c("Fat","Carbonates","Protein"),total=100))
 FatCarbonatesProtein
 soy 23.333 33.33 43.33
 peas 3.488 70.93 25.58
 wheat 1.190 71.43 27.38
 1The “SimpleData.txt” file can be downloaded from http://www.stat.boogaart.de/
 compositionsRBook.
2.1 APractical View to Compositional Concepts
 15
 corn 5.556
 beans 1.176
 83.33 11.11
 70.59 28.24
 These are now portions given in mass%. The command clo has an optional
 parameter parts taking the names or the numbers of columns to be selected and
 an optional argument total specifying the new reference total. If parts is not
 specified, implicitly all columns are used. If total is not specified, a default of 1 is
 assumed.
 Note that a naive interpretation of these numbers would now tell us that soy is
 the richest in protein (whereas before, it was rather in the middle of the ranking
 for protein amount), and that corn is now richer in carbonates than beans. This
 illustrates the main risk of interpreting raw portions: our conclusions can be
 opposite, depending on which composition we are observing.
 A composition only representing some of the possible components is called
 a subcomposition. Most of real compositional data is actually representing a
 subcomposition, as we never analyze each and every possible component of our
 samples. Aitchison (1986) therefore introduced the principle of subcompositional
 coherence: any compositional data analysis should be done in a way that we obtain
 the same results in a subcomposition, regardless of whether we analyzed only that
 subcomposition or a larger composition containing other parts. The interpretation
 we have been doing of the kitchen example up to now was not coherent, as our
 consideration of which food is richer in carbonates or protein changes between
 the original composition and the subcomposition of interest. This concern affects
 the whole direct application of all traditional multivariate statistics: none obey the
 principle of subcompositional coherence.
 Reclosure is often a controversial manipulation, as by closing a subcomposition,
 it seems that we lose some information on the total mass present in the parts
 considered, and apparently we replace some measured values by some computed
 ones. However, we must consider that some of the cases in the kitchen dataset are
 dry and other cookedconserves,some evenpre-salted, such that I do not need to add
 salt anymore. Some of the sodium (and of the other components, as well as water
 content) are thus artifacts of the form of delivery: they are related neither to the crop
 properties nor to the final cooked product we might eat. The only relevant quantities
 unaltered throughout these manipulations are the ratios between nutrients, and they
 are preserved by the closure operation.
 2.1.3 Completion of a Composition
 An alternative way to recast a vector of amounts to a composition is by adding
 the complementary variable. We first select the compositional variables using usual
 vectorized indexing2 with [] :
 2Call help("[") in R for a detailed explanation.
16
 2 Fundamental Concepts of Compositional Data Analysis
 > amounts <- kitchen[,c("Fat","Carbonates","Protein")]
 > amounts
 Fat Carbonates Protein
 soy 7.0
 10
 peas 3.0
 wheat 1.0
 corn 1.0
 beans 0.5
 61
 60
 15
 30
 13
 22
 23
 2
 12
 and then compute the total grams of each column by
 > (sumPresent <- totals(rplus(amounts)))
 soy peas wheat corn beans
 30.0 86.0 84.0 18.0 42.5
 This last line marks the dataset as positive real numbers by applying the rplus
 class3 and then computes the sum of the individual amounts, by rows with the
 commandtotals.
 We can now create a new dataset with an additional column Other giving the
 remaining mass:
 > Other <- kitchen[,"total"]- sumPresent
 > Amounts <- cbind(amounts,Other=Other)
 > clo(Amounts)
 Fat Carbonates Protein Other
 soy 0.07000
 0.1000 0.13000 0.7000
 peas 0.03000
 wheat 0.01000
 corn 0.01220
 beans 0.01176
 0.6100 0.22000 0.1400
 0.6000 0.23000 0.1600
 0.1829 0.02439 0.7805
 0.7059 0.28235
 NA
 The cbind command(for column bind) binds the columns of its arguments to a
 joint dataset containing all the columns.
 Of course, this procedure is only applicable when we know how much sample
 was analyzed in order to subtract from it the sum of the present components.
 This completion procedure has an advantage over the reclosure of the observed
 subcomposition: the reclosure loses all information about how much we had in total
 of the observed components, whereas in the completion procedure this total present
 is inherited by the “Other” part.
 A look at the last line reveals a practical problem of both the closure and the
 completion procedures in the presence of missing values. To close a composition
 with missing elements, we need to guess a value for the missing elements. The
 computer cannot add up unknown values and thus will close the rest of the
 3The different compositional classes will be discussed in Sect.2.4.
2.1 APractical View to Compositional Concepts
 17
 composition to 1, such as if this last components would be absent. However, it is
 very unlikely that the canned beans do not contain a considerable amount of water.
 2.1.4 Compositions as Equivalence Classes
 Either by reclosing a subcomposition or by completing the composition, the dataset
 is now compositional in nature, since its columns sum up to a constant. In any way,
 we homogenized the totals to the same quantity, that is to say: we removed the
 information on the total amount of each sample. This is licit, because that total is in
 our kitchen example not relevant to evaluate the nutritive character of the different
 crops: it is an arbitrary choice of the producer or due to the local unit system. In fact,
 wecould rescale each row of the dataset by multiplying it with a positive value, and
 the information they convey would not change. We can thus say that two vectors are
 compositionally equivalent (Aitchison, 1997; Barcel´o-Vidal, 2000) if one is simply
 some multiple of the other:
 a DA b, exists s>0foralli W ai D sbi
 In other words, all vectors with positive entries that are scaled versions of one
 another represent the same composition. For instance, the following three vectors
 are the same composition
 > Amounts[4,]*100/82
 Fat Carbonates Protein Other
 corn 1.22
 18.29 2.439 78.05
 > Amounts[4,]
 Fat Carbonates Protein Other
 corn 1
 15
 > acomp(Amounts[4,])
 Fat
 2
 64
 Carbonates Protein Other
 corn 0.0122 0.1829
 0.02439 0.7805
 attr(,"class")
 [1] acomp
 f
 irst giving the composition of corn as portions of 100g or mass%, then as portions
 of 82g, and finally, as relative portions of 1. Note that in the last case, we used the
 commandacomp,whichtells the system to consider its argumentas a compositional
 dataset, implicitly forcing a closure to 1. The different compositional classes (like
 acomp,orrplus before) are the grounding blocks of “compositions”and will be
 discussed in detail in Sect.2.4.
18
 2 Fundamental Concepts of Compositional Data Analysis
 2.1.5 Perturbation as a Change of Units
 Indeed mass percentage is a quite irrelevant type of quantification for us: we are
 more interested in the energy intake. But the different nutrients have a different
 energy content (in kJ/g):
 > (epm <- acomp(c(Fat=37,Protein=17,Carbonates=17)))
 Fat
 0.5211
 Protein Carbonates
 0.2394
 attr(,"class")
 [1] acomp
 0.2394
 Since our dataset is not anymore in grams but scaled to unit sum, it is likewise
 irrelevant in which units the energy contribution is given: the energy per mass can
 actually be seen again as a composition.
 We can now transform our mass percentage subcomposition coi of nutrients to
 a energy composition ec by scaling each entry with the corresponding energy per
 mass composition and reclosing. For example, for the first line of the dataset:
 .ec1i/iD1;:::;n D C .coi1iepmi/iD1;:::;n
 D .23:33 0:52;33:33 0:24;43:33 0:24/t
 23:33 0:52 C33:33 0:24C43:33 0:24
 D.0:4;0:26;0:34/t
 This operation is called perturbation. It is denoted by a ˚ sign:
 ec D coi˚epm
 In R, it is done simply by adding two acomp objects:
 > (ec <- acomp(coi)+epm)
 Fat
 Carbonates Protein
 soy 0.39846 0.2615
 0.3400
 peas 0.07293 0.6813
 wheat 0.02555 0.7044
 corn 0.11350 0.7822
 beans 0.02526 0.6962
 attr(,"class")
 [1] acomp
 0.2457
 0.2700
 0.1043
 0.2785
 Now,eccontainsthe compositionof myingredientsmeasured in terms of portionof
 energy rather than in mass. Perturbation is considered as the fundamental operation
 in compositional data analysis, equally important as the vector addition is in the
 usual real vector space RD. In an abstract mathematical view, perturbation is
2.1 APractical View to Compositional Concepts
 19
 indeed the addition in a vector space structure. Section2.5 formally introduces this
 operation and the properties of this vector space structure.
 2.1.6 Amalgamation
 A typical operation when dealing with compositional data is to amalgamate some
 of the variables into a single component. For instance, in the kitchen data reported
 before, fat was given as a single component. Though the original information gave
 the proportions of saturated and unsaturated fats,
 > fatDataset
 total.Fat sat.Fat
 soy
 peas
 wheat
 corn
 beans
 7
 3
 1
 1
 0.5
 1.40
 0.60
 0.25
 0.32
 0.10
 unsat.Fat
 5.60
 2.40
 0.75
 0.68
 0.10
 we amalgamated them into the total fats. Amalgamation, though very commonly
 done,is a quite dangerousmanipulation:a fair amountof informationis lost in a way
 that we may find ourselves unable to further work with the amalgamated dataset.
 For instance, if saturated and unsaturated fats have different energy content, we
 cannot compute the total fat mass proportion from the total fat mass energy content,
 or vice-versa. Amalgamation thus should only be applied in the “definition of the
 problem” stage, when choosing which variables will be considered and in which
 units. It should be meaningful in the units we are dealing with, because afterwards,
 we will not be able to change them. And it should have a deep connection with our
 questions: to amalgamate apples and pears may be meaningful when investigating
 the nutrition habits of one population (where an apple may be equivalent to a pear)
 and catastrophic when studying the importance of some fruits in several cultures
 (where we just want to see different patterns of preference of fruits). Once the parts
 are defined, the units chosen, and the questions posed, we should not amalgamate a
 variable anymore.
 The easiest way to amalgamate data in R is to explicitly compute the amalga
mated components as totals of an unclosed amount dataset (of an amount class,
 "rplus" or " aplus")containing only these components:
 > fatDataset$total.Fat = totals(aplus(fatDataset,
 c("sat.Fat","unsat.Fat")))#$
 The aplus command works in the same way as acomp but explicitly states that
 the total is still relevant and no reclosure should be performed. It is used to assign
 the scale of a dataset of amounts in relative geometry. Amalgamation is further
 explained in Sect.3.3.1.
20
 2 Fundamental Concepts of Compositional Data Analysis
 2.1.7 Missing Values and Outliers
 Note that the total amount for beans is missing (the nutrition table was reported for
 a 250mL can of this product). We could have more missing values in the dataset
 by considering potassium, which is only given for some of the products, probably
 those for which it is relevant. However, it would be naive to assume that there is no
 potassium in any of these products.
 Much more than in classical multivariate analysis, missing values in composi
tional data need a careful treatment, especially because a missing value in a single
 component makes it impossible to close or complete the data, such that finally, the
 actual portion of no single component is known. It will be thus very important to
 understand which procedures are still valid in the presence of missing values and
 how others might be (partially) mended. The same applies to many other kinds of
 irregularities in compositional data, like measurements with errors, atypical values,
 or values below the detection limit: in all these cases, the total sum will either be
 unknown or affected by an error, which will propagate to all the variables by the
 closure. The solution comes by realizing that whichever this total value might be,
 the (log)ratios between any regular components are unaffected.
 The “compositions” package will thus close the non-missing parts as if the
 missing parts are 0, knowing that this value will not affect any proper analysis