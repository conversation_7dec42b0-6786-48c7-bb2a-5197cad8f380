
Viterbi Algorithm for Hidden Markov Models (HMMs)
Last Updated : 23 Jul, 2025
The Viterbi algorithm is a dynamic programming algorithm for finding the most likely sequence of hidden states in a Hidden Markov Model (HMM). It is widely used in various applications such as speech recognition, bioinformatics, and natural language processing. This article delves into the fundamentals of the Viterbi algorithm, its applications, and a step-by-step guide to its implementation.

Table of Content

Understanding Hidden Markov Models (HMMs)
The Viterbi Algorithm
Initialization in Viterbi Algorithm
The Forward Algorithm
The Backward Algorithm
Decoding with Viterbi Algorithm
Optimizing Viterbi Algorithm
Example: Viterbi Algorithm in Action
Applications of Viterbi in HMM
Conclusion
Understanding Hidden Markov Models (HMMs)
A Hidden Markov Model (HMM) is a statistical model that represents systems with hidden states and observable events. It consists of:

States: Hidden conditions that are not directly observable.
Observations: Observable events influenced by the hidden states.
Transition Probabilities: Probabilities of moving from one state to another.
Emission Probabilities: Probabilities of observing a particular event given a state.
Initial State Probabilities: Probabilities of the system starting in a particular state.
Understanding HMM Parameters
To fully understand the Viterbi algorithm, it is essential to grasp the parameters of an HMM:

States (?): The set of hidden states in the model, denoted as 
?
=
?
1
,
?
2
,
…
,
?
N
?=? 
1
​
 ,? 
2
​
 ,…,? 
N
​
 .
Observations (?): The sequence of observed events, denoted as 
?
=
?
1
,
?
2
,
…
,
?
T
?=? 
1
​
 ,? 
2
​
 ,…,? 
T
​
 .
Transition Probabilities (?): The probability of transitioning from one state to another, denoted as 
?
=
?
i
j
?=? 
ij
​
  where 
?
i
j
=
?
(
?
j
∣
?
i
)
? 
ij
​
 =?(? 
j
​
 ∣? 
i
​
 ) .
Emission Probabilities (?): The probability of observing a particular event from a state, denoted as 
?
=
?
j
(
?
?
)
?=? 
j
​
 (? 
?
​
 ) where 
?
j
(
?
t
)
=
?
(
?
t
∣
?
j
)
? 
j
​
 (? 
t
​
 )=?(? 
t
​
 ∣? 
j
​
 ).
Initial Probabilities (?): The probability of starting in a particular state, denoted as 
π
=
{
π
i
}
π={π 
i
​
 }where 
π
i
=
P
(
s
i
 at 
t
=
1
)
π 
i
​
 =P(s 
i
​
  at t=1).
The Viterbi Algorithm
The Viterbi algorithm is a fundamental dynamic programming technique widely used in the context of Hidden Markov Models (HMMs) to uncover the most likely sequence of hidden states given a sequence of observed events.

Given a series of observed events, the Viterbi algorithm determines the most likely order of hidden states in an HMM. Andrew Viterbi, who first proposed the algorithm in 1967, is honored by the algorithm's name. The most likely sequence of states is computed efficiently by the Viterbi method, which divides the issue into smaller subproblems and solves them recursively.

The Viterbi algorithm operates by iteratively computing the highest probability paths to each state at each time step, storing these probabilities, and backtracking to determine the most probable sequence of hidden states. This method ensures an efficient and accurate decoding of hidden state sequences, making it indispensable for applications that require precise sequence analysis and pattern recognition.

Initialization in Viterbi Algorithm
Initialization is the first step of the Viterbi algorithm. It sets up the initial probabilities for the starting states based on the initial state probabilities and the emission probabilities for the first observation.

Mathematically it can be represented as:

V
1
(
j
)
=
π
j
.
b
j
(
o
1
)
∀
j
ϵ
{
1
,
.
.
.
,
N
}
V 
1
​
 (j)=π 
j
​
 .b 
j
​
 (o 
1
​
 )∀jϵ{1,...,N}

Path
j
(
1
)
=
[
j
]
∀
j
ϵ
{
1
,
.
.
.
,
N
}
Path 
j
​
 (1)=[j]∀jϵ{1,...,N}

The Forward Algorithm
The Forward Algorithm is used to compute the probability of observing a sequence of observations given an HMM. It is a dynamic programming algorithm that recursively calculates the probabilities of partial observation sequences.

Step 1: Initialization
α
1
(
j
)
=
π
j
.
b
j
(
o
1
)
α 
1
​
 (j)=π 
j
​
 .b 
j
​
 (o 
1
​
 )

Step 2: Recursion
α
t
(
j
)
=
∑
i
=
1
N
α
t
−
1
(
i
)
.
a
i
j
.
b
j
(
o
t
)
α 
t
​
 (j)=∑ 
i=1
N
​
 α 
t−1
​
 (i).a 
ij
​
 .b 
j
​
 (o 
t
​
 )

Step 3: Termination
P
(
O
∣
λ
)
=
∑
j
=
1
N
α
T
(
j
)
P(O∣λ)=∑ 
j=1
N
​
 α 
T
​
 (j)

The Backward Algorithm
The Backward Algorithm complements the Forward Algorithm by computing the probability of the ending part of the observation sequence, starting from a given state.

Step 1: Initialization
β
T
(
j
)
=
1
β 
T
​
 (j)=1

Step 2: Recursion
β
t
(
i
)
=
∑
j
=
1
N
β
t
+
1
(
j
)
.
a
i
j
.
b
j
(
o
t
+
1
)
β 
t
​
 (i)=∑ 
j=1
N
​
 β 
t+1
​
 (j).a 
ij
​
 .b 
j
​
 (o 
t+1
​
 )

Step 3: Termination
P
(
O
∣
λ
)
=
∑
j
=
1
N
π
j
.
b
j
(
o
1
)
.
β
1
(
j
)
P(O∣λ)=∑ 
j=1
N
​
 π 
j
​
 .b 
j
​
 (o 
1
​
 ).β 
1
​
 (j)

Decoding with Viterbi Algorithm
Decoding in the context of HMMs refers to determining the most likely sequence of hidden states given an observation sequence. The Viterbi algorithm achieves this by maximizing the probability of the hidden state sequence.

Recursion
V
t
(
j
)
=
max
⁡
i
[
V
t
−
1
(
i
)
.
a
i
j
.
b
j
(
o
t
)
]
∀
j
ϵ
{
1
,
.
.
.
,
N
}
V 
t
​
 (j)=max 
i
​
 [V 
t−1
​
 (i).a 
ij
​
 .b 
j
​
 (o 
t
​
 )]∀jϵ{1,...,N}

Path
j
(
t
)
=
[
arg max
⁡
i
[
V
t
−
1
(
i
)
.
a
i
j
]
,
j
]
Path 
j
​
 (t)=[argmax 
i
​
 [V 
t−1
​
 (i).a 
ij
​
 ],j]

Termination Step
P
∗
=
max
⁡
j
V
T
(
j
)
P∗=max 
j
​
 V 
T
​
 (j)

Best Path
=
arg max
⁡
j
V
T
(
j
)
Best Path=argmax 
j
​
 V 
T
​
 (j)

Optimizing Viterbi Algorithm
To optimize the Viterbi algorithm, consider the following:

Pruning: Reducing the state space by eliminating states with very low probabilities.
Logarithms: Using log probabilities to avoid underflow issues.
Beam Search: Keeping track of only the top kkk most likely paths at each step to reduce computational complexity.
Example: Viterbi Algorithm in Action
Consider an HMM with two states (Rainy, Sunny) and three observations (Walk, Shop, Clean). The following matrices define the model:

States: S={Rainy,Sunny}
Observations: O={Walk,Shop,Clean}
Transition Probabilities (A): 
A
=
(
0.7
0.3
0.4
0.6
)
A=( 
0.7
0.4
​
  
​
  
0.3
0.6
​
 )
Emission Probabilities (B): 
B
=
(
0.1
0.4
0.5
0.6
0.3
0.1
)
B=( 
0.1
0.6
​
  
​
  
0.4
0.3
​
  
​
  
0.5
0.1
​
 )
Initial Probabilities 
(
π
)
(π): 
π
=
(
0.6
0.4
)
π=( 
0.6
​
  
​
  
0.4
​
 )
Given the observation sequence (Walk, Shop, Clean), the Viterbi algorithm computes the most probable state sequence.


import numpy as np

def viterbi(obs, states, start_p, trans_p, emit_p):
    V = [{}]
    path = {}

    for y in states:
        V[0][y] = start_p[y] * emit_p[y][obs[0]]
        path[y] = [y]

    for t in range(1, len(obs)):
        V.append({})
        newpath = {}

        for y in states:
            (prob, state) = max(
                [(V[t-1][y0] * trans_p[y0][y] * emit_p[y][obs[t]], y0) for y0 in states]
            )
            V[t][y] = prob
            newpath[y] = path[state] + [y]

        path = newpath

    (prob, state) = max([(V[-1][y], y) for y in states])
    return (prob, path[state])

states = ('Rainy', 'Sunny')
observations = ('Walk', 'Shop', 'Clean')
start_probability = {'Rainy': 0.6, 'Sunny': 0.4}
transition_probability = {
   'Rainy' : {'Rainy': 0.7, 'Sunny': 0.3},
   'Sunny' : {'Rainy': 0.4, 'Sunny': 0.6},
   }
emission_probability = {
   'Rainy' : {'Walk': 0.1, 'Shop': 0.4, 'Clean': 0.5},
   'Sunny' : {'Walk': 0.6, 'Shop': 0.3, 'Clean': 0.1},
   }

print(viterbi(observations, states, start_probability, transition_probability, emission_probability))
Output:

(0.01344, ['Sunny', 'Rainy', 'Rainy'])
The output of the Viterbi algorithm implementation is a tuple: (0.01344, ['Sunny', 'Rainy', 'Rainy']).

Explanation of the Output
Probability (0.01344):
This value represents the highest probability of the most likely sequence of states that can produce the given observation sequence (Walk, Shop, Clean).
In other words, it is the probability of the observation sequence occurring given the most probable path of hidden states.
Most Probable State Sequence (['Sunny', 'Rainy', 'Rainy']):
This list represents the sequence of hidden states that has the highest probability of resulting in the observed sequence (Walk, Shop, Clean).
Each element in the list corresponds to the most probable state at each time step:
Sunny for the first observation (Walk)
Rainy for the second observation (Shop)
Rainy for the third observation (Clean)
Applications of Viterbi in HMM
The Viterbi algorithm is widely used in various applications:

Speech Recognition: The Viterbi algorithm in speech recognition converts spoken words into text by determining the most likely word or phoneme sequence that corresponds to the detected acoustic signals. HMMs simulate the speech process by using observations to represent acoustic features and hidden states to represent phonemes.
Bioinformatics: The Viterbi algorithm is used in bioinformatics to align DNA sequences, predict protein structures, and locate gene sequences. Biological sequences can be modeled using HMMs, where observations correspond to sequences of nucleotides or amino acids, and hidden states represent biological functions.
Natural Language Processing: In NLP, the Viterbi algorithm is used for tasks such as part-of-speech tagging, named entity recognition, and machine translation. HMMs can model linguistic sequences, with hidden states representing grammatical categories and observations representing words.
Error Correction: In digital communications, the Viterbi algorithm is used for error correction to decode transmitted messages that may have been corrupted by noise. HMMs can model the transmission process, with hidden states representing the transmitted symbols and observations representing the received symbols.
Conclusion
A key component of sequence analysis in HMMs is the Viterbi algorithm, which provides a reliable way to infer the most likely order of hidden states from events that have been seen. Through comprehension of its principles and uses, one can effectively utilize the algorithm to address complicated issues in a variety of domains. Because of its versatility and effectiveness, the Viterbi algorithm is a vital tool in contemporary computational analysis.
# The Viterbi graph

As we stated in the previous section, in both the crooked dealer and CG-island HMMs, we are looking for the most likely hidden path π for an HMM that emits a string $$x$$. In other words, we would like to maximize $$\Pr(x, \pi)$$ among all possible hidden paths $$\pi$$.

> **Decoding Problem:** *Find an optimal hidden path in an HMM given a string of its emitted symbols.*
>
> * **Input:** A string $$x = x_1 \dots x_n$$ emitted by an $$\text{HMM}(\Sigma, \text{States}, \text{Transition}, \text{Emission})$$.
>
> * **Output:** A path $$\pi$$ that maximizes the probability $$\Pr(x, \pi)$$ over all possible paths through this HMM.

In 1967, Andrew Viterbi used an HMM-inspired analog of a Manhattan-like grid to solve the Decoding Problem. For an HMM emitting a string of $$n$$ symbols $$x = x_1 \dots x_n$$, the nodes in the HMM’s **Viterbi graph** are divided into $$|\text{States}|$$ rows and $$n$$ columns (see figure below). That is, node $$(k, i)$$ represents state $$k$$ and the *i*-th emitted symbol. Each node is connected to all nodes in the column to its right; the edge connecting $$(l, i − 1)$$ to $$(k, i)$$ corresponds to transitioning from state $$l$$ to state $$k$$ (with probability $$\text{transition}(l,k)$$) and then emitting symbol $$x_i$$ (with probability $$\text{emission}_k(x_i)$$). As a result, every path connecting a node in the first column of the Viterbi graph to a node in the final column corresponds to a hidden path $$\pi = \pi_1 \dots \pi_n$$.

We assign a weight of

$$\text{Weight}_i(l,k) = \text{transition}_{\pi_{i-1},\pi_i} \cdot \text{emission}_{\pi_i}(x_i)$$

to the edge connecting $$(l, i − 1)$$ to $$(k, i)$$ in the Viterbi graph. Furthermore, we define the **product weight** of a path in the Viterbi graph as the product of its edge weights. For a path from the leftmost column to the rightmost column in the Viterbi graph corresponding to the hidden path $$\pi$$, this product weight is equal to the product of $$n − 1$$ terms,

$$\prod_{i=2}^n \text{transition}_{\pi_{i-1},\pi_i} \cdot \text{emission}_{\pi_i}(x_i) = \prod_{i=2}^n \text{Weight}_i(l,k)$$.

> **STOP and Think:** How does this expression differ from the formula for $$\Pr(x, \pi)$$ that we derived in the previous section?

![The diagram of an HMM with three states (emission/transition probabilities as well as nodes corresponding to emitted symbols are omitted).](https://acny3m5l6yl0.feishu.cn/space/api/box/stream/download/asynccode/?code=YWJhODE2OWUyYThkMmE2NzZkYzg4MGMxMDcwNjFlNTdfczVobUgyVHhjbWNlU2dEUGU0cUtnMWs1ME9MTGFzMVBfVG9rZW46VjRRT2Ixd2s2bzl3Uk54MUV2WGN5Y0NCblFoXzE3NTYzNjgzNTg6MTc1NjM3MTk1OF9WNA)

&#x20;

![Given a string of n symbols x = x\_1 . . . x\_n emitted by an HMM, Viterbi’s Manhattan is a grid with |States| rows and n columns in which each node is connected to every node in the column to its right. The weight of the edge connecting (l, i − 1) to (k, i) is Weight(l, k) = transition\_{l, k} · emission\_k(x\_i). Unlike the alignment graphs we encountered previously, in which the set of valid directions was restricted to south, east, and southeast edges, every node in a column is connected by an edge to every node in the column to its right in the Viterbi graph.](https://acny3m5l6yl0.feishu.cn/space/api/box/stream/download/asynccode/?code=ZDExNTBhMTIwMzBhOGU0NWQ2MzY2NGRhNDNhYWU3YjFfTXFER2Nvd1BwTGhqZlZ5eGtrWGVlcEc1VTVFTTZ1N0tfVG9rZW46SGdrWGJZMHA1bzEzeGN4UmJ6T2N2bmZIbnZnXzE3NTYzNjgzNTg6MTc1NjM3MTk1OF9WNA)

The only difference between the expression

$$\prod_{i=2}^n \text{transition}_{\pi_{i-1},\pi_i} \cdot \text{emission}_{\pi_i}(x_i) = \prod_{i=2}^n \text{Weight}_i(l,k)$$

And the expression that we obtained for $$\Pr(x, \pi)$$,

$$\prod_{i=1}^n \text{transition}_{\pi_{i-1},\pi_i} \cdot \text{emission}_{\pi_i}(x_i)$$

is the single factor $$\text{transition}_{\pi_0,\pi_1} \cdot \text{emission}_{\pi_1}(x_1)$$, which corresponds to transitioning from the initial state $$\pi_0$$ to $$\pi_1$$ and emitting the first symbol. To model the initial state, we will add a source node *source* to the Viterbi graph and then connect *source* to each node $$(k, 1)$$ in the first column with an edge of weight $$\text{Weight}_1(source, k) = \text{transition}_{\pi_0, k} \cdot \text{emission}_k(x_1)$$. We will also assume that the HMM has another silent **terminal state** that the HMM enters when it has finished emitting symbols. To model the terminal state, we add a sink node *sink* to the Viterbi graph and connect every node in the last column to *sink* with an edge of weight 1 (see figure below).

![The Viterbi graph with additional source node (blue) and sink node (red). A path of largest product weight connecting the source to the sink corresponds to an optimal hidden path solving the Decoding Problem.](https://acny3m5l6yl0.feishu.cn/space/api/box/stream/download/asynccode/?code=OGQzOTdlNWRmZDAyMzExZGQ0ZjBiM2RlNmQxMTI5MGNfU2w3YmRsTE5nNHBNRGFXaThOUmlqNFd1TUxpVmYxTmpfVG9rZW46TFZCWWJuY1p3b3NNQnR4STJobmNpb0VjblBjXzE3NTYzNjgzNTg6MTc1NjM3MTk1OF9WNA)

Every hidden path $$\pi$$ in the HMM now corresponds to a path from *source* to *sink* in the Viterbi graph with product weight $$\Pr(x, \pi)$$. Therefore, the Decoding Problem reduces to finding a path in the Viterbi graph of largest product weight over all paths connecting *source* to *sink*.

**Exercise Break:** Find the maximum product weight path in the Viterbi graph for the crooked dealer HMM (whose HMM diagram is reproduced below) when *x* = “HHTT”.  Express your answer as a string of four "F" and "B" symbols.

**Note:&#x20;**&#x59;ou may assume that transitions from the initial state occur with equal probability.

![](https://acny3m5l6yl0.feishu.cn/space/api/box/stream/download/asynccode/?code=MzJjYmNmM2ViNzkzYzkzZWQ5NjQyMjIyNzUwM2U2MTNfRDZCWVQ5eFNqVjBNNEhNMGRHWFNVU3BJdGVyTzlCTk1fVG9rZW46VllheWJleVltb2hKV3p4Zm1zV2NQYlNSbnViXzE3NTYzNjgzNTg6MTc1NjM3MTk1OF9WNA)

# The Viterbi algorithm

We will apply a dynamic programming algorithm to solve the Decoding Problem. First, define $$s_{k,i}$$ as the product weight of an optimal path (i.e., a path with maximum product weight) from *source* to the node $$(k, i)$$. The **Viterbi algorithm** relies on the fact that the first $$i − 1$$ edges of an optimal path from *source* to $$(k, i)$$ must form an optimal path from *source* to $$(l, i − 1)$$ for some (unknown) state $$l$$. This observation yields the following recurrence:

$$\begin{aligned}
s_{k,i} &= \max_{\text{all states }l}\left\{ s_{l, i-1} \cdot (\text{weight of edge between nodes }(l, i-1) \text{ and } (k,i) ) \right\} \\
&=  \max_{\text{all states }l}\left\{ s_{l, i-1} \cdot \text{Weight}_i(l, k) \right\} \\
&= \max_{\text{all states }l}\left\{ s_{l, i-1} \cdot \text{transition}_{\pi_{i-1},\pi_i} \cdot \text{emission}_{\pi_i}(x_i) \right\}
\end{aligned}$$

Since *source* is connected to every node in the first column of the Viterbi graph,

$$\begin{aligned}
s_{k,1} &= s_{source} \cdot (\text{weight of edge between source and } (k,1) ) \\
&= s_{source} \cdot \text{Weight}_0(source, k) \\
&= s_{source} \cdot \text{transition}_{source,k} \cdot \text{emission}_k(x_1)
\end{aligned}$$

In order to initialize this recurrence, we set $$s_{source}$$ equal to 1. We can now compute the maximum product weight over all paths from *source* to *sink* as

$$s_{sink} = \max_{\text{all states }l}s_{l,n}$$

> **STOP and Think:** How can we adapt our algorithm for finding a longest path in a DAG to find a path with maximum product weight?

# How fast is the Viterbi algorithm?

We can interpret the Decoding Problem as yet another instance of the Longest Path in a DAG Problem from our work on sequence alignment because the path $$\pi$$ maximizing the product weight $$\prod_{i=1}^n \text{Weight}_i(\pi_{i-1},\pi_i)$$ also maximizes the logarithm of this product, which is equal to $$\sum_{i=1}^n \log(\text{Weight}_i(\pi_{i-1}))$$. Thus, we can substitute the weights of all edges in the Viterbi graph by their logarithms. Finding a longest path (i.e. a path maximizing the *sum* of edge weights) in the resulting graph will correspond to a path of maximum *product* weight in the original Viterbi graph. For this reason, the runtime of the Viterbi algorithm, which you are now ready to implement, is linear in the number of edges in the Viterbi graph. The following exercise shows that the number of these edges is $$O(|\text{States}|^2 \cdot n)$$, where $$n$$ is the number of emitted symbols.

**Exercise Break:** Show that the number of edges in the Viterbi graph of an HMM emitting a string of length $$n$$ is $$|\text{States}|^2 \cdot (n − 1) + 2 \cdot |\text{States}|$$.

> **Code Challenge:&#x20;**&#x49;mplement the Viterbi algorithm solving the [Decoding Problem](https://rosalind.info/problems/ba10c/).
>
> * **Input:&#x20;**&#x41; string $$x$$, followed by the alphabet from which $$x$$ was constructed, followed by the states *States*, transition matrix *Transition*, and emission matrix *Emission* of an $$\text{HMM}(\Sigma, \text{States}, \text{Transition}, \text{Emission})$$.
>
> * **Output:&#x20;**&#x41; path that maximizes the (unconditional) probability $$\Pr(x, \pi)$$ over all possible paths $$\pi$$.

**Exercise Break:** Apply your solution for the Decoding Problem to find CG-islands in the first million nucleotides from the [human X chromosome](https://github.com/BioinformaticsAlgorithms/BioinformaticsAlgorithms.github.io/blob/main/data/realdatasets/HMM/chrX.txt) (given in FASTA format). To help you design an HMM for this application, you may assume that transitions from CG-islands to non-CG-islands are rare, occurring with probability 0.001, and that transitions from non-CG-islands to CG-islands are even more rare, occurring with probability 0.0001. How many CG-islands do you find?

In practice, many HMMs have forbidden transitions between some states. For such transitions, we can safely remove the corresponding edges from the HMM diagram (figure below (top)). This operation results in a sparser Viterbi graph (figure below (bottom)), which reduces the runtime of the Viterbi algorithm, since the runtime of the algorithm for finding the longest path in a DAG is linear in the number of edges in the DAG.

**Exercise Break:** Let *Edges* denote the set of edges in the diagram of an HMM that may have some forbidden transitions. Prove that the number of edges in the Viterbi graph for this HMM is $$|\text{Edges}| \cdot (n − 1) + 2 \cdot |\text{States}|$$.

![An HMM diagram for an HMM that has four states with some forbidden transitions, such as from A to D and from C to itself. Edges corresponding to forbidden transitions between states are not included in the HMM diagram.](https://acny3m5l6yl0.feishu.cn/space/api/box/stream/download/asynccode/?code=YzZkMTExZDg4ZjBkNmNjOGU5ZTY5MjBhZDJhMDE5NjJfc0htdzlVRGRleHV2WmRHNmZCeDhtV3JxZkE1U2xrSFdfVG9rZW46WFRiR2JvNnNRb3dVMUN4Ull4MmNBdmtPbm1iXzE3NTYzNjgzNTg6MTc1NjM3MTk1OF9WNA)

![The Viterbi graph for this HMM emitting a string of length 6.](https://acny3m5l6yl0.feishu.cn/space/api/box/stream/download/asynccode/?code=Yjk1ODU2MGYyNjQwYzAxNGIxNzI5YmFlODJjYTIyMGZfWTdyUFlUd0pSS25sUkFnQ01hcFFkd2FxcHZveUl3SjhfVG9rZW46S3hDNmJHNkw2b3FUMDB4QzFVV2NjYkxQbm9nXzE3NTYzNjgzNTg6MTc1NjM3MTk1OF9WNA)

