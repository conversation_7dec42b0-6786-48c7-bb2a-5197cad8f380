

WikipediaThe Free Encyclopedia
Search Wikipedia
Search
Donate
Create account
Log in

Contents hide
(Top)
The algorithm

The Q-matrix
Distance from the pair members to the new node
Distance of the other taxa from the new node
Complexity
Example

First step
First joining
First branch length estimation
First distance matrix update
Second step
Second joining
Second branch length estimation
Second distance matrix update
Final step
Conclusion: additive distances
Neighbor joining as minimum evolution
Advantages and disadvantages
Implementations and variants
See also
References

Other sources
External links
Neighbor joining

Article
Talk
Read
Edit
View history

Tools
Appearance hide
Text

Small

Standard

Large
Width

Standard

Wide
Color (beta)

Automatic

Light

Dark
From Wikipedia, the free encyclopedia
In bioinformatics, neighbor joining is a bottom-up (agglomerative) clustering method for the creation of phylogenetic trees, created by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 1987.[1] Usually based on DNA or protein sequence data, the algorithm requires knowledge of the distance between each pair of taxa (e.g., species or sequences) to create the phylogenetic tree.[2]

The algorithm

Starting with a star tree (A), the Q matrix is calculated and used to choose a pair of nodes for joining, in this case f and g. These are joined to a newly created node, u, as shown in (B). The part of the tree shown as solid lines is now fixed and will not be changed in subsequent joining steps. The distances from node u to the nodes a-e are computed from equation (3). This process is then repeated, using a matrix of just the distances between the nodes, a,b,c,d,e, and u, and a Q matrix derived from it. In this case u and e are joined to the newly created v, as shown in (C). Two more iterations lead first to (D), and then to (E), at which point the algorithm is done, as the tree is fully resolved.
Neighbor joining takes a distance matrix, which specifies the distance between each pair of taxa, as input. The algorithm starts with a completely unresolved tree, whose topology corresponds to that of a star network, and iterates over the following steps, until the tree is completely resolved, and all branch lengths are known:

Based on the current distance matrix, calculate a matrix 
Q
{\displaystyle Q} (defined below).
Find the pair of distinct taxa i and j (i.e. with 
i
≠
j
{\displaystyle i\neq j}) for which 
Q
(
i
,
j
)
{\displaystyle Q(i,j)} is smallest. Make a new node that joins the taxa i and j, and connect the new node to the central node. For example, in part (B) of the figure at right, node u is created to join f and g.
Calculate the distance from each of the taxa in the pair to this new node.
Calculate the distance from each of the taxa outside of this pair to the new node.
Start the algorithm again, replacing the pair of joined neighbors with the new node and using the distances calculated in the previous step.
The Q-matrix
Based on a distance matrix relating the 
n
{\displaystyle n} taxa, calculate the 
n
{\displaystyle n} x 
n
{\displaystyle n} matrix 
Q
{\displaystyle Q} as follows:

Q
(
i
,
j
)
=
(
n
−
2
)
d
(
i
,
j
)
−
∑
k
=
1
n
d
(
i
,
k
)
−
∑
k
=
1
n
d
(
j
,
k
)
{\displaystyle Q(i,j)=(n-2)d(i,j)-\sum _{k=1}^{n}d(i,k)-\sum _{k=1}^{n}d(j,k)}		1
where 
d
(
i
,
j
)
{\displaystyle d(i,j)} is the distance between taxa 
i
{\displaystyle i} and 
j
{\displaystyle j}.

Distance from the pair members to the new node
For each of the taxa in the pair being joined, use the following formula to calculate the distance to the new node:

δ
(
f
,
u
)
=
1
2
d
(
f
,
g
)
+
1
2
(
n
−
2
)
[
∑
k
=
1
n
d
(
f
,
k
)
−
∑
k
=
1
n
d
(
g
,
k
)
]
{\displaystyle \delta (f,u)={\frac {1}{2}}d(f,g)+{\frac {1}{2(n-2)}}\left[\sum _{k=1}^{n}d(f,k)-\sum _{k=1}^{n}d(g,k)\right]\quad }		2
and:

δ
(
g
,
u
)
=
d
(
f
,
g
)
−
δ
(
f
,
u
)
{\displaystyle \delta (g,u)=d(f,g)-\delta (f,u)\quad }
Taxa 
f
{\displaystyle f} and 
g
{\displaystyle g} are the paired taxa and 
u
{\displaystyle u} is the newly created node. The branches joining 
f
{\displaystyle f} and 
u
{\displaystyle u} and 
g
{\displaystyle g} and 
u
{\displaystyle u}, and their lengths, 
δ
(
f
,
u
)
{\displaystyle \delta (f,u)} and 
δ
(
g
,
u
)
{\displaystyle \delta (g,u)} are part of the tree which is gradually being created; they neither affect nor are affected by later neighbor-joining steps.

Distance of the other taxa from the new node
For each taxon not considered in the previous step, we calculate the distance to the new node as follows:

d
(
u
,
k
)
=
1
2
[
d
(
f
,
k
)
+
d
(
g
,
k
)
−
d
(
f
,
g
)
]
{\displaystyle d(u,k)={\frac {1}{2}}[d(f,k)+d(g,k)-d(f,g)]}		3
where 
u
{\displaystyle u} is the new node, 
k
{\displaystyle k} is the node which we want to calculate the distance to and 
f
{\displaystyle f} and 
g
{\displaystyle g} are the members of the pair just joined.

Complexity
Neighbor joining on a set of 
n
{\displaystyle n} taxa requires 
n
−
3
{\displaystyle n-3} iterations. At each step one has to build and search a 
Q
{\displaystyle Q} matrix. Initially the 
Q
{\displaystyle Q} matrix is size 
n
×
n
{\displaystyle n\times n}, then the next step it is 
(
n
−
1
)
×
(
n
−
1
)
{\displaystyle (n-1)\times (n-1)}, etc. Implementing this in a straightforward way leads to an algorithm with a time complexity of 
O
(
n
3
)
{\displaystyle O(n^{3})};[3] implementations exist which use heuristics to do much better than this on average.[4]

Example

Neighbor joining with 5 taxa. In this case 2 neighbor joining steps give a tree with fully resolved topology. The branches of the resulting tree are labeled with their lengths.

Let us assume that we have five taxa 
(
a
,
b
,
c
,
d
,
e
)
{\displaystyle (a,b,c,d,e)} and the following distance matrix 
D
{\displaystyle D}:

a	b	c	d	e
a	0	5	9	9	8
b	5	0	10	10	9
c	9	10	0	8	7
d	9	10	8	0	3
e	8	9	7	3	0
First step
First joining
We calculate the 
Q
1
{\displaystyle Q_{1}} values by equation (1). For example:

Q
1
(
a
,
b
)
=
(
n
−
2
)
d
(
a
,
b
)
−
∑
k
=
1
5
d
(
a
,
k
)
−
∑
k
=
1
5
d
(
b
,
k
)
{\displaystyle Q_{1}(a,b)=(n-2)d(a,b)-\sum _{k=1}^{5}d(a,k)-\sum _{k=1}^{5}d(b,k)}
=
(
5
−
2
)
×
5
−
(
5
+
9
+
9
+
8
)
−
(
5
+
10
+
10
+
9
)
=
15
−
31
−
34
=
−
50
{\displaystyle =(5-2)\times 5-(5+9+9+8)-(5+10+10+9)=15-31-34=-50}
We obtain the following values for the 
Q
1
{\displaystyle Q_{1}} matrix (the diagonal elements of the matrix are not used and are omitted here):

a	b	c	d	e
a		−50	−38	−34	−34
b	−50		−38	−34	−34
c	−38	−38		−40	−40
d	−34	−34	−40		−48
e	−34	−34	−40	−48	
In the example above, 
Q
1
(
a
,
b
)
=
−
50
{\displaystyle Q_{1}(a,b)=-50}. This is the smallest value of 
Q
1
{\displaystyle Q_{1}}, so we join elements 
a
{\displaystyle a} and 
b
{\displaystyle b}.

First branch length estimation
Let 
u
{\displaystyle u} denote the new node. By equation (2), above, the branches joining 
a
{\displaystyle a} and 
b
{\displaystyle b} to 
u
{\displaystyle u} then have lengths:

δ
(
a
,
u
)
=
1
2
d
(
a
,
b
)
+
1
2
(
5
−
2
)
[
∑
k
=
1
5
d
(
a
,
k
)
−
∑
k
=
1
5
d
(
b
,
k
)
]
=
5
2
+
31
−
34
6
=
2
{\displaystyle \delta (a,u)={\frac {1}{2}}d(a,b)+{\frac {1}{2(5-2)}}\left[\sum _{k=1}^{5}d(a,k)-\sum _{k=1}^{5}d(b,k)\right]\quad ={\frac {5}{2}}+{\frac {31-34}{6}}=2}
δ
(
b
,
u
)
=
d
(
a
,
b
)
−
δ
(
a
,
u
)
=
5
−
2
=
3
{\displaystyle \delta (b,u)=d(a,b)-\delta (a,u)\quad =5-2=3}
First distance matrix update
We then proceed to update the initial distance matrix 
D
{\displaystyle D} into a new distance matrix 
D
1
{\displaystyle D_{1}} (see below), reduced in size by one row and one column because of the joining of 
a
{\displaystyle a} with 
b
{\displaystyle b} into their neighbor 
u
{\displaystyle u}. Using equation (3) above, we compute the distance from 
u
{\displaystyle u} to each of the other nodes besides 
a
{\displaystyle a} and 
b
{\displaystyle b}. In this case, we obtain:

d
(
u
,
c
)
=
1
2
[
d
(
a
,
c
)
+
d
(
b
,
c
)
−
d
(
a
,
b
)
]
=
9
+
10
−
5
2
=
7
{\displaystyle d(u,c)={\frac {1}{2}}[d(a,c)+d(b,c)-d(a,b)]={\frac {9+10-5}{2}}=7}
d
(
u
,
d
)
=
1
2
[
d
(
a
,
d
)
+
d
(
b
,
d
)
−
d
(
a
,
b
)
]
=
9
+
10
−
5
2
=
7
{\displaystyle d(u,d)={\frac {1}{2}}[d(a,d)+d(b,d)-d(a,b)]={\frac {9+10-5}{2}}=7}
d
(
u
,
e
)
=
1
2
[
d
(
a
,
e
)
+
d
(
b
,
e
)
−
d
(
a
,
b
)
]
=
8
+
9
−
5
2
=
6
{\displaystyle d(u,e)={\frac {1}{2}}[d(a,e)+d(b,e)-d(a,b)]={\frac {8+9-5}{2}}=6}
The resulting distance matrix 
D
1
{\displaystyle D_{1}} is:

u	c	d	e
u	0	7	7	6
c	7	0	8	7
d	7	8	0	3
e	6	7	3	0
Bold values in 
D
1
{\displaystyle D_{1}} correspond to the newly calculated distances, whereas italicized values are not affected by the matrix update as they correspond to distances between elements not involved in the first joining of taxa.

Second step
Second joining
The corresponding 
Q
2
{\displaystyle Q_{2}} matrix is:

u	c	d	e
u		−28	−24	−24
c	−28		−24	−24
d	−24	−24		−28
e	−24	−24	−28	
We may choose either to join 
u
{\displaystyle u} and 
c
{\displaystyle c}, or to join 
d
{\displaystyle d} and 
e
{\displaystyle e}; both pairs have the minimal 
Q
2
{\displaystyle Q_{2}} value of 
−
28
{\displaystyle -28}, and either choice leads to the same result. For concreteness, let us join 
u
{\displaystyle u} and 
c
{\displaystyle c} and call the new node 
v
{\displaystyle v}.

Second branch length estimation
The lengths of the branches joining 
u
{\displaystyle u} and 
c
{\displaystyle c} to 
v
{\displaystyle v} can be calculated:

δ
(
u
,
v
)
=
1
2
d
(
u
,
c
)
+
1
2
(
4
−
2
)
[
∑
k
=
1
4
d
(
u
,
k
)
−
∑
k
=
1
4
d
(
c
,
k
)
]
=
7
2
+
20
−
22
4
=
3
{\displaystyle \delta (u,v)={\frac {1}{2}}d(u,c)+{\frac {1}{2(4-2)}}\left[\sum _{k=1}^{4}d(u,k)-\sum _{k=1}^{4}d(c,k)\right]\quad ={\frac {7}{2}}+{\frac {20-22}{4}}=3}
δ
(
c
,
v
)
=
d
(
u
,
c
)
−
δ
(
u
,
v
)
=
7
−
3
=
4
{\displaystyle \delta (c,v)=d(u,c)-\delta (u,v)\quad =7-3=4}
The joining of the elements and the branch length calculation help drawing the neighbor joining tree as shown in the figure.

Second distance matrix update
The updated distance matrix 
D
2
{\displaystyle D_{2}} for the remaining 3 nodes, 
v
{\displaystyle v}, 
d
{\displaystyle d}, and 
e
{\displaystyle e}, is now computed:

d
(
v
,
d
)
=
1
2
[
d
(
u
,
d
)
+
d
(
c
,
d
)
−
d
(
u
,
c
)
]
=
7
+
8
−
7
2
=
4
{\displaystyle d(v,d)={\frac {1}{2}}[d(u,d)+d(c,d)-d(u,c)]={\frac {7+8-7}{2}}=4}
d
(
v
,
e
)
=
1
2
[
d
(
u
,
e
)
+
d
(
c
,
e
)
−
d
(
u
,
c
)
]
=
6
+
7
−
7
2
=
3
{\displaystyle d(v,e)={\frac {1}{2}}[d(u,e)+d(c,e)-d(u,c)]={\frac {6+7-7}{2}}=3}
v	d	e
v	0	4	3
d	4	0	3
e	3	3	0
Final step
The tree topology is fully resolved at this point. However, for clarity, we can calculate the 
Q
3
{\displaystyle Q_{3}} matrix. For example:

Q
3
(
v
,
e
)
=
(
3
−
2
)
d
(
v
,
e
)
−
∑
k
=
1
3
d
(
v
,
k
)
−
∑
k
=
1
3
d
(
e
,
k
)
=
3
−
7
−
6
=
−
10
{\displaystyle Q_{3}(v,e)=(3-2)d(v,e)-\sum _{k=1}^{3}d(v,k)-\sum _{k=1}^{3}d(e,k)=3-7-6=-10}
v	d	e
v		−10	−10
d	−10		−10
e	−10	−10	
For concreteness, let us join 
v
{\displaystyle v} and 
d
{\displaystyle d} and call the last node 
w
{\displaystyle w}. The lengths of the three remaining branches can be calculated:

δ
(
v
,
w
)
=
1
2
d
(
v
,
d
)
+
1
2
(
3
−
2
)
[
∑
k
=
1
3
d
(
v
,
k
)
−
∑
k
=
1
3
d
(
d
,
k
)
]
=
4
2
+
7
−
7
2
=
2
{\displaystyle \delta (v,w)={\frac {1}{2}}d(v,d)+{\frac {1}{2(3-2)}}\left[\sum _{k=1}^{3}d(v,k)-\sum _{k=1}^{3}d(d,k)\right]\quad ={\frac {4}{2}}+{\frac {7-7}{2}}=2}
δ
(
w
,
d
)
=
d
(
v
,
d
)
−
δ
(
v
,
w
)
=
4
−
2
=
2
{\displaystyle \delta (w,d)=d(v,d)-\delta (v,w)=4-2=2}
δ
(
w
,
e
)
=
d
(
v
,
e
)
−
δ
(
v
,
w
)
=
3
−
2
=
1
{\displaystyle \delta (w,e)=d(v,e)-\delta (v,w)=3-2=1}
The neighbor joining tree is now complete, as shown in the figure.

Conclusion: additive distances
This example represents an idealized case: note that if we move from any taxon to any other along the branches of the tree, and sum the lengths of the branches traversed, the result is equal to the distance between those taxa in the input distance matrix. For example, going from 
d
{\displaystyle d} to 
b
{\displaystyle b} we have 
2
+
2
+
3
+
3
=
10
{\displaystyle 2+2+3+3=10}. A distance matrix whose distances agree in this way with some tree is said to be 'additive', a property which is rare in practice. Nonetheless it is important to note that, given an additive distance matrix as input, neighbor joining is guaranteed to find the tree whose distances between taxa agree with it.

Neighbor joining as minimum evolution
Neighbor joining may be viewed as a greedy heuristic for the balanced minimum evolution[5] (BME) criterion. For each topology, BME defines the tree length (sum of branch lengths) to be a particular weighted sum of the distances in the distance matrix, with the weights depending on the topology. The BME optimal topology is the one which minimizes this tree length. NJ at each step greedily joins that pair of taxa which will give the greatest decrease in the estimated tree length. This procedure does not guarantee to find the optimum for the BME criterion, although it often does and is usually quite close.[5]

Advantages and disadvantages
The main virtue of NJ is that it is fast[6]: 466  as compared to least squares, maximum parsimony and maximum likelihood methods.[6] This makes it practical for analyzing large data sets (hundreds or thousands of taxa) and for bootstrapping, for which purposes other means of analysis (e.g. maximum parsimony, maximum likelihood) may be computationally prohibitive.

Neighbor joining has the property that if the input distance matrix is correct, then the output tree will be correct. Furthermore, the correctness of the output tree topology is guaranteed as long as the distance matrix is 'nearly additive', specifically if each entry in the distance matrix differs from the true distance by less than half of the shortest branch length in the tree.[7] In practice the distance matrix rarely satisfies this condition, but neighbor joining often constructs the correct tree topology anyway.[8] The correctness of neighbor joining for nearly additive distance matrices implies that it is statistically consistent under many models of evolution; given data of sufficient length, neighbor joining will reconstruct the true tree with high probability. Compared with UPGMA and WPGMA, neighbor joining has the advantage that it does not assume all lineages evolve at the same rate (molecular clock hypothesis).

Nevertheless, neighbor joining has been largely superseded by phylogenetic methods that do not rely on distance measures and offer superior accuracy under most conditions.[citation needed] Neighbor joining has the undesirable feature that it often assigns negative lengths to some of the branches.

Implementations and variants
There are many programs available implementing neighbor joining. Among implementations of canonical NJ (i.e. using the classical NJ optimisation criteria, therefore giving the same results), RapidNJ (started 2003, major update in 2011, still updated in 2023)[9] and NINJA (started 2009, last update 2013)[10] are considered state-of-the-art. They have typical run times proportional to approximately the square of the number of taxa.

Variants that deviate from canonical include:

BIONJ (1997)[11] and Weighbor (2000),[12] improving on the accuracy by making use of the fact that the shorter distances in the distance matrix are generally better known than the longer distances. The two methods have been extended to run on incomplete distance matrices.[13]
"Fast NJ" remembers the best node and is O(n^2) always; "relax NJ" performs a hill-climbing search and retains the worst-case complexity of O(n^3). Rapid NJ is faster than plain relaxed NJ.[14]
FastME is an implementation of the closely related balanced minimum evolution (BME) method (see § Neighbor joining as minimum evolution). It is about as fast as and more accurate than NJ. It starts with a rough tree then improves it using a set of topological moves such as Nearest Neighbor Interchanges (NNI).[15] FastTree is a related method. It works on sequence "profiles" instead of a matrix. It starts with an approximately NJ tree, rearranges it into BME, then rearranges it into approximate maximum-likelihood.[16]
See also
Nearest neighbor search
UPGMA and WPGMA
Minimum Evolution
References
 Saitou, N.; Nei, M. (1 July 1987). "The neighbor-joining method: a new method for reconstructing phylogenetic trees". Molecular Biology and Evolution. 4 (4): 406–425. doi:10.1093/oxfordjournals.molbev.a040454. PMID 3447015.
 Xavier Didelot (2010). "Sequence-Based Analysis of Bacterial Population Structures". In D. Ashley Robinson; Daniel Falush; Edward J. Feil (eds.). Bacterial Population Genetics in Infectious Disease. John Wiley and Sons. pp. 46–47. ISBN 978-0-470-42474-2.
 Studier, J. A.; Keppler, K. J. (November 1988). "A note on the neighbor-joining algorithm of Saitou and Nei". Molecular Biology and Evolution. 5 (6): 729–31. doi:10.1093/oxfordjournals.molbev.a040527. ISSN 1537-1719. PMID 3221794.
 Mailund, Thomas; Brodal, GerthS; Fagerberg, Rolf; Pedersen, ChristianNS; Phillips, Derek (2006). "Recrafting the neighbor-joining method". BMC Bioinformatics. 7 (1): 29. doi:10.1186/1471-2105-7-29. PMC 3271233. PMID 16423304.
 Gascuel O, Steel M (2006). "Neighbor-joining revealed". Mol Biol Evol. 23 (11): 1997–2000. doi:10.1093/molbev/msl072. PMID 16877499.
 Kuhner, M. K.; Felsenstein, J. (1994-05-01). "A simulation comparison of phylogeny algorithms under equal and unequal evolutionary rates". Molecular Biology and Evolution. 11 (3): 459–468. doi:10.1093/oxfordjournals.molbev.a040126. ISSN 0737-4038. PMID 8015439.
 Atteson K (1997). "The performance of neighbor-joining algorithms of phylogeny reconstruction", pp. 101–110. In Jiang, T., and Lee, D., eds., Lecture Notes in Computer Science, 1276, Springer-Verlag, Berlin. COCOON '97.
 Mihaescu R, Levy D, Pachter L (2009). "Why neighbor-joining works". Algorithmica. 54 (1): 1–24. arXiv:cs/0602041. doi:10.1007/s00453-007-9116-4. S2CID 2462145.
 "RapidNJ". birc.au.dk.
 "NINJA: a tool for large-scale neighbor-joining phylogeny inference - Home". wheelerlab.org.
 "ATGC: BioNJ". www.atgc-montpellier.fr.
 "WEIGHBOR Homepage". 5 March 2015. Archived from the original on 2015-03-05.
 Criscuolo, Alexis; Gascuel, Olivier (December 2008). "Fast NJ-like algorithms to deal with incomplete distance matrices". BMC Bioinformatics. 9 (1): 166. doi:10.1186/1471-2105-9-166. PMC 2335114. PMID 18366787.
 Simonsen, Martin; Mailund, Thomas; Pedersen, Christian N. S. (2008). "Rapid Neighbour-Joining" (PDF). Algorithms in Bioinformatics. Lecture Notes in Computer Science. Vol. 5251. pp. 113–122. doi:10.1007/978-3-540-87361-7_10. ISBN 978-3-540-87360-0.
 "ATGC: FastME". www.atgc-montpellier.fr.
 "FastTree 2.1: Approximately-Maximum-Likelihood Trees for Large Alignments". www.microbesonline.org.
Other sources
Studier JA, Keppler KJ (1988). "A note on the Neighbor-Joining algorithm of Saitou and Nei". Mol Biol Evol. 5 (6): 729–731. doi:10.1093/oxfordjournals.molbev.a040527. PMID 3221794.
Martin Simonsen; Thomas Mailund; Christian N. S. Pedersen (2008). "Rapid Neighbour-Joining". Algorithms in Bioinformatics. Lecture Notes in Computer Science. Vol. 5251. pp. 113–122. CiteSeerX **********.2078. doi:10.1007/978-3-540-87361-7_10. ISBN 978-3-540-87360-0.
External links
The Neighbor-Joining Method — a tutorial
vte
Phylogenetics
Relevant fields	
Computational phylogeneticsMolecular phylogeneticsCladisticsTaxonomyEvolutionary taxonomySystematics
icon	Evolutionary biology portal
Basic concepts	
Phylogenesis CladogenesisPhylogenetic treeCladogramPhylogenetic networkLong branch attractionClade vs GradeLineage Ghost lineageGhost population
Inference methods	
Maximum parsimonyPhylogenetic reconciliationProbabilistic methods Maximum likelihoodBayesian inferenceDistance-matrix methods Neighbor-joiningUPGMALeast squaresThree-taxon analysis
Current topics	
PhyloCodeDNA barcodingMolecular phylogeneticsPhylogenetic comparative methodsPhylogenetic niche conservatismPhylogenetic signalPhylogenetics softwarePhylogenomicsPhylogeography
Group traits	
Primitive PlesiomorphySymplesiomorphyDerived ApomorphySynapomorphyAutapomorphy
Group types	
MonophylyParaphylyPolyphyly
Nomenclature	
Phylogenetic nomenclatureCrown groupSister groupBasalSupertree
 Category Commons
Categories: Bioinformatics algorithmsPhylogeneticsComputational phylogeneticsCluster analysis algorithms
This page was last edited on 17 January 2025, at 17:42 (UTC).
Text is available under the Creative Commons Attribution-ShareAlike 4.0 License; additional terms may apply. By using this site, you agree to the Terms of Use and Privacy Policy. Wikipedia® is a registered trademark of the Wikimedia Foundation, Inc., a non-profit organization.
Privacy policyAbout WikipediaDisclaimersContact WikipediaCode of ConductDevelopersStatisticsCookie statementMobile view
Wikimedia Foundation
Powered by MediaWiki
