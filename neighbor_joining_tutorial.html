<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neighbor Joining Algorithm Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
            color: white;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #4facfe;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }
        
        .algorithm-step {
            background: #e8f4fd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .formula-box {
            background: #f1f2f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .matrix {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        table {
            border-collapse: collapse;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .tree-visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-counter {
            background: #4facfe;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        
        .navigation ul {
            list-style: none;
        }
        
        .navigation li {
            margin: 5px 0;
        }
        
        .navigation a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .navigation a:hover {
            color: #4facfe;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(79, 172, 254, 0.4);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#algorithm">Algorithm</a></li>
            <li><a href="#mathematics">Mathematics</a></li>
            <li><a href="#example">Example</a></li>
            <li><a href="#complexity">Complexity</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>Neighbor Joining Algorithm</h1>
            <p>A Comprehensive Tutorial on Phylogenetic Tree Construction</p>
        </div>

        <section id="introduction" class="section">
            <h2>🌳 Introduction to Neighbor Joining</h2>
            
            <div class="highlight-box">
                <strong>What is Phylogenetic Reconstruction?</strong><br>
                Phylogenetic reconstruction is the process of determining the evolutionary relationships between different species or sequences. It creates tree-like structures that show how organisms are related through common ancestors.
            </div>

            <h3>Why Neighbor Joining?</h3>
            <p>Neighbor Joining (NJ) is a bottom-up clustering method created by Naruya Saitou and Masatoshi Nei in 1987. It's particularly valuable because:</p>
            
            <ul style="margin: 20px 0; padding-left: 30px;">
                <li><strong>Speed:</strong> Much faster than maximum likelihood or parsimony methods</li>
                <li><strong>Accuracy:</strong> Produces correct trees when distance matrices are additive</li>
                <li><strong>No Clock Assumption:</strong> Unlike UPGMA, doesn't assume constant evolutionary rates</li>
                <li><strong>Scalability:</strong> Can handle hundreds or thousands of taxa</li>
            </ul>

            <div class="tree-visualization">
                <h4>Basic Concept Visualization</h4>
                <svg width="600" height="200" viewBox="0 0 600 200">
                    <!-- Star tree (initial) -->
                    <g transform="translate(100, 100)">
                        <circle cx="0" cy="0" r="5" fill="#e74c3c"/>
                        <line x1="0" y1="0" x2="-60" y2="-40" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="0" x2="-60" y2="40" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="0" x2="60" y2="-40" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="0" x2="60" y2="40" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="0" x2="0" y2="-60" stroke="#34495e" stroke-width="2"/>
                        
                        <circle cx="-60" cy="-40" r="8" fill="#3498db"/>
                        <circle cx="-60" cy="40" r="8" fill="#3498db"/>
                        <circle cx="60" cy="-40" r="8" fill="#3498db"/>
                        <circle cx="60" cy="40" r="8" fill="#3498db"/>
                        <circle cx="0" cy="-60" r="8" fill="#3498db"/>
                        
                        <text x="-70" y="-35" font-size="12" fill="#2c3e50">A</text>
                        <text x="-70" y="45" font-size="12" fill="#2c3e50">B</text>
                        <text x="70" y="-35" font-size="12" fill="#2c3e50">C</text>
                        <text x="70" y="45" font-size="12" fill="#2c3e50">D</text>
                        <text x="5" y="-65" font-size="12" fill="#2c3e50">E</text>
                        
                        <text x="-30" y="80" font-size="14" fill="#2c3e50" text-anchor="middle">Initial Star Tree</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 220 100 L 280 100" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                    
                    <!-- Final tree -->
                    <g transform="translate(400, 100)">
                        <!-- Tree structure -->
                        <line x1="0" y1="0" x2="-40" y2="-30" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="-40" y2="30" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="40" y2="0" stroke="#27ae60" stroke-width="3"/>
                        
                        <line x1="-40" y1="-30" x2="-70" y2="-50" stroke="#27ae60" stroke-width="2"/>
                        <line x1="-40" y1="-30" x2="-70" y2="-10" stroke="#27ae60" stroke-width="2"/>
                        
                        <line x1="-40" y1="30" x2="-70" y2="10" stroke="#27ae60" stroke-width="2"/>
                        <line x1="-40" y1="30" x2="-70" y2="50" stroke="#27ae60" stroke-width="2"/>
                        
                        <!-- Nodes -->
                        <circle cx="0" cy="0" r="5" fill="#e74c3c"/>
                        <circle cx="-40" cy="-30" r="5" fill="#e74c3c"/>
                        <circle cx="-40" cy="30" r="5" fill="#e74c3c"/>
                        <circle cx="40" cy="0" r="8" fill="#3498db"/>
                        <circle cx="-70" cy="-50" r="8" fill="#3498db"/>
                        <circle cx="-70" cy="-10" r="8" fill="#3498db"/>
                        <circle cx="-70" cy="10" r="8" fill="#3498db"/>
                        <circle cx="-70" cy="50" r="8" fill="#3498db"/>
                        
                        <!-- Labels -->
                        <text x="50" y="5" font-size="12" fill="#2c3e50">E</text>
                        <text x="-80" y="-45" font-size="12" fill="#2c3e50">A</text>
                        <text x="-80" y="-5" font-size="12" fill="#2c3e50">B</text>
                        <text x="-80" y="15" font-size="12" fill="#2c3e50">C</text>
                        <text x="-80" y="55" font-size="12" fill="#2c3e50">D</text>
                        
                        <text x="-30" y="80" font-size="14" fill="#2c3e50" text-anchor="middle">Final Phylogenetic Tree</text>
                    </g>
                </svg>
            </div>
        </section>

        <section id="algorithm" class="section">
            <h2>⚙️ The Algorithm Overview</h2>

            <p>The Neighbor Joining algorithm follows a systematic approach to build phylogenetic trees. Here's the step-by-step process:</p>

            <div class="algorithm-step">
                <span class="step-counter">1</span>
                <strong>Start with a Star Tree:</strong> Begin with all taxa connected to a central node (completely unresolved topology).
            </div>

            <div class="algorithm-step">
                <span class="step-counter">2</span>
                <strong>Calculate Q-Matrix:</strong> Based on the current distance matrix, compute the Q-matrix using the formula:
                <div class="formula-box">
                    $$Q(i,j) = (n-2)d(i,j) - \sum_{k=1}^{n}d(i,k) - \sum_{k=1}^{n}d(j,k)$$
                </div>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">3</span>
                <strong>Find Minimum Q-Value:</strong> Identify the pair of taxa $(i,j)$ with the smallest $Q(i,j)$ value.
            </div>

            <div class="algorithm-step">
                <span class="step-counter">4</span>
                <strong>Join the Pair:</strong> Create a new internal node that connects the selected pair of taxa.
            </div>

            <div class="algorithm-step">
                <span class="step-counter">5</span>
                <strong>Calculate Branch Lengths:</strong> Determine the distances from each taxon in the pair to the new node.
            </div>

            <div class="algorithm-step">
                <span class="step-counter">6</span>
                <strong>Update Distance Matrix:</strong> Calculate distances from the new node to all remaining taxa.
            </div>

            <div class="algorithm-step">
                <span class="step-counter">7</span>
                <strong>Repeat:</strong> Continue until only three taxa remain (tree is fully resolved).
            </div>

            <div class="tree-visualization">
                <h4>Algorithm Progression Visualization</h4>
                <svg width="800" height="300" viewBox="0 0 800 300">
                    <!-- Step A: Star tree -->
                    <g transform="translate(100, 150)">
                        <circle cx="0" cy="0" r="4" fill="#e74c3c"/>
                        <line x1="0" y1="0" x2="-40" y2="-30" stroke="#95a5a6" stroke-width="2"/>
                        <line x1="0" y1="0" x2="-40" y2="30" stroke="#95a5a6" stroke-width="2"/>
                        <line x1="0" y1="0" x2="40" y2="-30" stroke="#95a5a6" stroke-width="2"/>
                        <line x1="0" y1="0" x2="40" y2="30" stroke="#95a5a6" stroke-width="2"/>
                        <line x1="0" y1="0" x2="0" y2="-50" stroke="#95a5a6" stroke-width="2"/>

                        <circle cx="-40" cy="-30" r="6" fill="#3498db"/>
                        <circle cx="-40" cy="30" r="6" fill="#3498db"/>
                        <circle cx="40" cy="-30" r="6" fill="#3498db"/>
                        <circle cx="40" cy="30" r="6" fill="#3498db"/>
                        <circle cx="0" cy="-50" r="6" fill="#3498db"/>

                        <text x="-50" y="-25" font-size="10" fill="#2c3e50">a</text>
                        <text x="-50" y="35" font-size="10" fill="#2c3e50">b</text>
                        <text x="50" y="-25" font-size="10" fill="#2c3e50">c</text>
                        <text x="50" y="35" font-size="10" fill="#2c3e50">d</text>
                        <text x="5" y="-55" font-size="10" fill="#2c3e50">e</text>

                        <text x="0" y="80" font-size="12" fill="#2c3e50" text-anchor="middle">A) Initial Star</text>
                    </g>

                    <!-- Arrow 1 -->
                    <path d="M 160 150 L 200 150" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>

                    <!-- Step B: First join -->
                    <g transform="translate(280, 150)">
                        <circle cx="0" cy="0" r="4" fill="#e74c3c"/>
                        <circle cx="-30" cy="0" r="4" fill="#e74c3c"/>

                        <!-- Solid lines (fixed part) -->
                        <line x1="-30" y1="0" x2="-60" y2="-20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="-30" y1="0" x2="-60" y2="20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="-30" y2="0" stroke="#27ae60" stroke-width="3"/>

                        <!-- Dashed lines (remaining connections) -->
                        <line x1="0" y1="0" x2="40" y2="-30" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="0" y1="0" x2="40" y2="30" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="0" y1="0" x2="0" y2="-50" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>

                        <circle cx="-60" cy="-20" r="6" fill="#3498db"/>
                        <circle cx="-60" cy="20" r="6" fill="#3498db"/>
                        <circle cx="40" cy="-30" r="6" fill="#3498db"/>
                        <circle cx="40" cy="30" r="6" fill="#3498db"/>
                        <circle cx="0" cy="-50" r="6" fill="#3498db"/>

                        <text x="-70" y="-15" font-size="10" fill="#2c3e50">a</text>
                        <text x="-70" y="25" font-size="10" fill="#2c3e50">b</text>
                        <text x="50" y="-25" font-size="10" fill="#2c3e50">c</text>
                        <text x="50" y="35" font-size="10" fill="#2c3e50">d</text>
                        <text x="5" y="-55" font-size="10" fill="#2c3e50">e</text>
                        <text x="-35" y="15" font-size="10" fill="#e74c3c">u</text>

                        <text x="0" y="80" font-size="12" fill="#2c3e50" text-anchor="middle">B) Join a,b → u</text>
                    </g>

                    <!-- Arrow 2 -->
                    <path d="M 340 150 L 380 150" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrow2)"/>

                    <!-- Step C: Second join -->
                    <g transform="translate(460, 150)">
                        <circle cx="0" cy="0" r="4" fill="#e74c3c"/>
                        <circle cx="-30" cy="0" r="4" fill="#e74c3c"/>
                        <circle cx="30" cy="0" r="4" fill="#e74c3c"/>

                        <!-- Solid lines (fixed parts) -->
                        <line x1="-30" y1="0" x2="-60" y2="-20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="-30" y1="0" x2="-60" y2="20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="-30" y2="0" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="30" y2="0" stroke="#27ae60" stroke-width="3"/>
                        <line x1="30" y1="0" x2="60" y2="-20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="30" y1="0" x2="60" y2="20" stroke="#27ae60" stroke-width="3"/>

                        <!-- Remaining connection -->
                        <line x1="0" y1="0" x2="0" y2="-50" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>

                        <circle cx="-60" cy="-20" r="6" fill="#3498db"/>
                        <circle cx="-60" cy="20" r="6" fill="#3498db"/>
                        <circle cx="60" cy="-20" r="6" fill="#3498db"/>
                        <circle cx="60" cy="20" r="6" fill="#3498db"/>
                        <circle cx="0" cy="-50" r="6" fill="#3498db"/>

                        <text x="-70" y="-15" font-size="10" fill="#2c3e50">a</text>
                        <text x="-70" y="25" font-size="10" fill="#2c3e50">b</text>
                        <text x="70" y="-15" font-size="10" fill="#2c3e50">c</text>
                        <text x="70" y="25" font-size="10" fill="#2c3e50">d</text>
                        <text x="5" y="-55" font-size="10" fill="#2c3e50">e</text>
                        <text x="-35" y="15" font-size="10" fill="#e74c3c">u</text>
                        <text x="35" y="15" font-size="10" fill="#e74c3c">v</text>

                        <text x="0" y="80" font-size="12" fill="#2c3e50" text-anchor="middle">C) Join c,d → v</text>
                    </g>

                    <!-- Arrow 3 -->
                    <path d="M 520 150 L 560 150" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrow3)"/>

                    <!-- Step D: Final tree -->
                    <g transform="translate(640, 150)">
                        <circle cx="0" cy="0" r="4" fill="#e74c3c"/>
                        <circle cx="-30" cy="0" r="4" fill="#e74c3c"/>
                        <circle cx="30" cy="0" r="4" fill="#e74c3c"/>

                        <!-- All solid lines (complete tree) -->
                        <line x1="-30" y1="0" x2="-60" y2="-20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="-30" y1="0" x2="-60" y2="20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="-30" y2="0" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="30" y2="0" stroke="#27ae60" stroke-width="3"/>
                        <line x1="30" y1="0" x2="60" y2="-20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="30" y1="0" x2="60" y2="20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="0" y1="0" x2="0" y2="-50" stroke="#27ae60" stroke-width="3"/>

                        <circle cx="-60" cy="-20" r="6" fill="#3498db"/>
                        <circle cx="-60" cy="20" r="6" fill="#3498db"/>
                        <circle cx="60" cy="-20" r="6" fill="#3498db"/>
                        <circle cx="60" cy="20" r="6" fill="#3498db"/>
                        <circle cx="0" cy="-50" r="6" fill="#3498db"/>

                        <text x="-70" y="-15" font-size="10" fill="#2c3e50">a</text>
                        <text x="-70" y="25" font-size="10" fill="#2c3e50">b</text>
                        <text x="70" y="-15" font-size="10" fill="#2c3e50">c</text>
                        <text x="70" y="25" font-size="10" fill="#2c3e50">d</text>
                        <text x="5" y="-55" font-size="10" fill="#2c3e50">e</text>
                        <text x="-35" y="15" font-size="10" fill="#e74c3c">u</text>
                        <text x="35" y="15" font-size="10" fill="#e74c3c">v</text>

                        <text x="0" y="80" font-size="12" fill="#2c3e50" text-anchor="middle">D) Complete Tree</text>
                    </g>

                    <!-- Arrow definitions -->
                    <defs>
                        <marker id="arrow1" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                        </marker>
                        <marker id="arrow2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                        </marker>
                        <marker id="arrow3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="highlight-box">
                <strong>Key Insight:</strong> The algorithm progressively resolves the tree structure by identifying the most closely related pairs of taxa at each step. The Q-matrix helps identify these pairs by considering not just pairwise distances, but also the overall distance relationships.
            </div>
        </section>

        <section id="mathematics" class="section">
            <h2>📐 Mathematical Foundation</h2>

            <p>The mathematical formulas behind Neighbor Joining are crucial for understanding how the algorithm works. Let's break down each component:</p>

            <h3>1. The Q-Matrix Formula</h3>
            <p>The Q-matrix is the heart of the algorithm. For any pair of taxa $i$ and $j$, the Q-value is calculated as:</p>

            <div class="formula-box">
                $$Q(i,j) = (n-2)d(i,j) - \sum_{k=1}^{n}d(i,k) - \sum_{k=1}^{n}d(j,k)$$
            </div>

            <div class="highlight-box">
                <strong>Understanding the Q-Matrix:</strong><br>
                • $(n-2)d(i,j)$: Amplifies the direct distance between taxa $i$ and $j$<br>
                • $\sum_{k=1}^{n}d(i,k)$: Sum of all distances from taxon $i$ to all other taxa<br>
                • $\sum_{k=1}^{n}d(j,k)$: Sum of all distances from taxon $j$ to all other taxa<br><br>
                The Q-matrix identifies pairs that are close to each other but far from everything else.
            </div>

            <h3>2. Branch Length Calculations</h3>
            <p>When joining taxa $f$ and $g$ to create new node $u$, the branch lengths are:</p>

            <div class="formula-box">
                $$\delta(f,u) = \frac{1}{2}d(f,g) + \frac{1}{2(n-2)}\left[\sum_{k=1}^{n}d(f,k) - \sum_{k=1}^{n}d(g,k)\right]$$
            </div>

            <div class="formula-box">
                $$\delta(g,u) = d(f,g) - \delta(f,u)$$
            </div>

            <div class="algorithm-step">
                <strong>Branch Length Intuition:</strong><br>
                The first formula splits the distance $d(f,g)$ between the two branches, then adjusts based on how far each taxon is from all others. If taxon $f$ is generally farther from other taxa than $g$, then $f$ gets a longer branch.
            </div>

            <h3>3. Distance Matrix Update</h3>
            <p>After joining taxa $f$ and $g$ into node $u$, we calculate distances from $u$ to all remaining taxa $k$:</p>

            <div class="formula-box">
                $$d(u,k) = \frac{1}{2}[d(f,k) + d(g,k) - d(f,g)]$$
            </div>

            <div class="tree-visualization">
                <h4>Mathematical Visualization: Q-Matrix Calculation</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Distance matrix visualization -->
                    <g transform="translate(50, 50)">
                        <text x="100" y="20" font-size="16" fill="#2c3e50" text-anchor="middle">Distance Matrix D</text>

                        <!-- Matrix grid -->
                        <rect x="20" y="40" width="160" height="160" fill="none" stroke="#34495e" stroke-width="2"/>

                        <!-- Headers -->
                        <text x="10" y="75" font-size="12" fill="#2c3e50">a</text>
                        <text x="10" y="105" font-size="12" fill="#2c3e50">b</text>
                        <text x="10" y="135" font-size="12" fill="#2c3e50">c</text>
                        <text x="10" y="165" font-size="12" fill="#2c3e50">d</text>
                        <text x="10" y="195" font-size="12" fill="#2c3e50">e</text>

                        <text x="50" y="35" font-size="12" fill="#2c3e50">a</text>
                        <text x="80" y="35" font-size="12" fill="#2c3e50">b</text>
                        <text x="110" y="35" font-size="12" fill="#2c3e50">c</text>
                        <text x="140" y="35" font-size="12" fill="#2c3e50">d</text>
                        <text x="170" y="35" font-size="12" fill="#2c3e50">e</text>

                        <!-- Matrix values -->
                        <text x="50" y="75" font-size="11" fill="#2c3e50" text-anchor="middle">0</text>
                        <text x="80" y="75" font-size="11" fill="#e74c3c" text-anchor="middle">5</text>
                        <text x="110" y="75" font-size="11" fill="#2c3e50" text-anchor="middle">9</text>
                        <text x="140" y="75" font-size="11" fill="#2c3e50" text-anchor="middle">9</text>
                        <text x="170" y="75" font-size="11" fill="#2c3e50" text-anchor="middle">8</text>

                        <text x="50" y="105" font-size="11" fill="#e74c3c" text-anchor="middle">5</text>
                        <text x="80" y="105" font-size="11" fill="#2c3e50" text-anchor="middle">0</text>
                        <text x="110" y="105" font-size="11" fill="#2c3e50" text-anchor="middle">10</text>
                        <text x="140" y="105" font-size="11" fill="#2c3e50" text-anchor="middle">10</text>
                        <text x="170" y="105" font-size="11" fill="#2c3e50" text-anchor="middle">9</text>

                        <text x="50" y="135" font-size="11" fill="#2c3e50" text-anchor="middle">9</text>
                        <text x="80" y="135" font-size="11" fill="#2c3e50" text-anchor="middle">10</text>
                        <text x="110" y="135" font-size="11" fill="#2c3e50" text-anchor="middle">0</text>
                        <text x="140" y="135" font-size="11" fill="#2c3e50" text-anchor="middle">8</text>
                        <text x="170" y="135" font-size="11" fill="#2c3e50" text-anchor="middle">7</text>

                        <text x="50" y="165" font-size="11" fill="#2c3e50" text-anchor="middle">9</text>
                        <text x="80" y="165" font-size="11" fill="#2c3e50" text-anchor="middle">10</text>
                        <text x="140" y="165" font-size="11" fill="#2c3e50" text-anchor="middle">0</text>
                        <text x="110" y="165" font-size="11" fill="#2c3e50" text-anchor="middle">8</text>
                        <text x="170" y="165" font-size="11" fill="#2c3e50" text-anchor="middle">3</text>

                        <text x="50" y="195" font-size="11" fill="#2c3e50" text-anchor="middle">8</text>
                        <text x="80" y="195" font-size="11" fill="#2c3e50" text-anchor="middle">9</text>
                        <text x="110" y="195" font-size="11" fill="#2c3e50" text-anchor="middle">7</text>
                        <text x="140" y="195" font-size="11" fill="#2c3e50" text-anchor="middle">3</text>
                        <text x="170" y="195" font-size="11" fill="#2c3e50" text-anchor="middle">0</text>

                        <!-- Highlight d(a,b) -->
                        <rect x="70" y="65" width="20" height="20" fill="#e74c3c" fill-opacity="0.3" stroke="#e74c3c" stroke-width="2"/>
                    </g>

                    <!-- Arrow -->
                    <path d="M 250 150 L 300 150" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#calc-arrow)"/>

                    <!-- Q-matrix calculation -->
                    <g transform="translate(320, 50)">
                        <text x="100" y="20" font-size="16" fill="#2c3e50" text-anchor="middle">Q-Matrix Calculation</text>

                        <text x="0" y="50" font-size="12" fill="#2c3e50">For Q(a,b):</text>
                        <text x="0" y="70" font-size="11" fill="#2c3e50">Q(a,b) = (n-2)d(a,b) - Σd(a,k) - Σd(b,k)</text>
                        <text x="0" y="90" font-size="11" fill="#2c3e50">= (5-2)×5 - (0+5+9+9+8) - (5+0+10+10+9)</text>
                        <text x="0" y="110" font-size="11" fill="#2c3e50">= 15 - 31 - 34</text>
                        <text x="0" y="130" font-size="11" fill="#e74c3c" font-weight="bold">= -50</text>

                        <!-- Q-matrix result -->
                        <text x="0" y="170" font-size="14" fill="#2c3e50">Resulting Q-Matrix:</text>

                        <rect x="20" y="190" width="160" height="130" fill="none" stroke="#34495e" stroke-width="2"/>

                        <!-- Q-matrix values -->
                        <text x="50" y="210" font-size="11" fill="#2c3e50" text-anchor="middle">-</text>
                        <text x="80" y="210" font-size="11" fill="#e74c3c" text-anchor="middle" font-weight="bold">-50</text>
                        <text x="110" y="210" font-size="11" fill="#2c3e50" text-anchor="middle">-38</text>
                        <text x="140" y="210" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>
                        <text x="170" y="210" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>

                        <text x="50" y="230" font-size="11" fill="#e74c3c" text-anchor="middle" font-weight="bold">-50</text>
                        <text x="80" y="230" font-size="11" fill="#2c3e50" text-anchor="middle">-</text>
                        <text x="110" y="230" font-size="11" fill="#2c3e50" text-anchor="middle">-38</text>
                        <text x="140" y="230" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>
                        <text x="170" y="230" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>

                        <text x="50" y="250" font-size="11" fill="#2c3e50" text-anchor="middle">-38</text>
                        <text x="80" y="250" font-size="11" fill="#2c3e50" text-anchor="middle">-38</text>
                        <text x="110" y="250" font-size="11" fill="#2c3e50" text-anchor="middle">-</text>
                        <text x="140" y="250" font-size="11" fill="#2c3e50" text-anchor="middle">-40</text>
                        <text x="170" y="250" font-size="11" fill="#2c3e50" text-anchor="middle">-40</text>

                        <text x="50" y="270" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>
                        <text x="80" y="270" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>
                        <text x="110" y="270" font-size="11" fill="#2c3e50" text-anchor="middle">-40</text>
                        <text x="140" y="270" font-size="11" fill="#2c3e50" text-anchor="middle">-</text>
                        <text x="170" y="270" font-size="11" fill="#2c3e50" text-anchor="middle">-48</text>

                        <text x="50" y="290" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>
                        <text x="80" y="290" font-size="11" fill="#2c3e50" text-anchor="middle">-34</text>
                        <text x="110" y="290" font-size="11" fill="#2c3e50" text-anchor="middle">-40</text>
                        <text x="140" y="290" font-size="11" fill="#2c3e50" text-anchor="middle">-48</text>
                        <text x="170" y="290" font-size="11" fill="#2c3e50" text-anchor="middle">-</text>

                        <!-- Highlight minimum -->
                        <rect x="70" y="200" width="20" height="20" fill="#e74c3c" fill-opacity="0.3" stroke="#e74c3c" stroke-width="2"/>
                        <rect x="40" y="220" width="20" height="20" fill="#e74c3c" fill-opacity="0.3" stroke="#e74c3c" stroke-width="2"/>

                        <text x="100" y="340" font-size="12" fill="#e74c3c" text-anchor="middle" font-weight="bold">Minimum Q(a,b) = -50</text>
                        <text x="100" y="355" font-size="11" fill="#2c3e50" text-anchor="middle">→ Join taxa a and b</text>
                    </g>

                    <defs>
                        <marker id="calc-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="highlight-box">
                <strong>Why These Formulas Work:</strong><br>
                The Q-matrix formula balances two competing factors: it favors pairs with small direct distances but penalizes pairs where one or both taxa are far from everything else. This ensures we join taxa that are genuinely close relatives, not just outliers.
            </div>
        </section>

        <section id="example" class="section">
            <h2>🔍 Detailed Example: 5-Taxa Analysis</h2>

            <p>Let's work through a complete example with 5 taxa (a, b, c, d, e) to see how Neighbor Joining constructs a phylogenetic tree step by step.</p>

            <h3>Initial Distance Matrix</h3>
            <p>We start with the following distance matrix representing evolutionary distances between our 5 taxa:</p>

            <div class="matrix">
                <table>
                    <tr>
                        <th></th><th>a</th><th>b</th><th>c</th><th>d</th><th>e</th>
                    </tr>
                    <tr>
                        <th>a</th><td>0</td><td style="background: #ffeb3b;">5</td><td>9</td><td>9</td><td>8</td>
                    </tr>
                    <tr>
                        <th>b</th><td style="background: #ffeb3b;">5</td><td>0</td><td>10</td><td>10</td><td>9</td>
                    </tr>
                    <tr>
                        <th>c</th><td>9</td><td>10</td><td>0</td><td>8</td><td>7</td>
                    </tr>
                    <tr>
                        <th>d</th><td>9</td><td>10</td><td>8</td><td>0</td><td style="background: #4caf50;">3</td>
                    </tr>
                    <tr>
                        <th>e</th><td>8</td><td>9</td><td>7</td><td style="background: #4caf50;">3</td><td>0</td>
                    </tr>
                </table>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">1</span>
                <strong>Step 1: Calculate Q₁ Matrix</strong>
                <p>Using the Q-matrix formula with n=5:</p>
                <div class="formula-box">
                    $$Q_1(a,b) = (5-2) \times 5 - (0+5+9+9+8) - (5+0+10+10+9) = 15 - 31 - 34 = -50$$
                </div>

                <div class="matrix">
                    <table>
                        <tr>
                            <th></th><th>a</th><th>b</th><th>c</th><th>d</th><th>e</th>
                        </tr>
                        <tr>
                            <th>a</th><td>-</td><td style="background: #e74c3c; color: white;"><strong>-50</strong></td><td>-38</td><td>-34</td><td>-34</td>
                        </tr>
                        <tr>
                            <th>b</th><td style="background: #e74c3c; color: white;"><strong>-50</strong></td><td>-</td><td>-38</td><td>-34</td><td>-34</td>
                        </tr>
                        <tr>
                            <th>c</th><td>-38</td><td>-38</td><td>-</td><td>-40</td><td>-40</td>
                        </tr>
                        <tr>
                            <th>d</th><td>-34</td><td>-34</td><td>-40</td><td>-</td><td>-48</td>
                        </tr>
                        <tr>
                            <th>e</th><td>-34</td><td>-34</td><td>-40</td><td>-48</td><td>-</td>
                        </tr>
                    </table>
                </div>

                <p><strong>Result:</strong> Q₁(a,b) = -50 is the minimum value, so we join taxa a and b.</p>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">2</span>
                <strong>Step 2: Calculate Branch Lengths for a-u and b-u</strong>
                <p>Create new internal node u connecting a and b:</p>

                <div class="formula-box">
                    $$\delta(a,u) = \frac{1}{2} \times 5 + \frac{1}{2(5-2)}[31 - 34] = 2.5 - 0.5 = 2$$
                </div>

                <div class="formula-box">
                    $$\delta(b,u) = 5 - 2 = 3$$
                </div>
            </div>

            <div class="tree-visualization">
                <h4>Tree After First Join</h4>
                <svg width="400" height="200" viewBox="0 0 400 200">
                    <g transform="translate(200, 100)">
                        <!-- Central node -->
                        <circle cx="0" cy="0" r="4" fill="#e74c3c"/>

                        <!-- New internal node u -->
                        <circle cx="-60" cy="0" r="4" fill="#e74c3c"/>
                        <line x1="0" y1="0" x2="-60" y2="0" stroke="#27ae60" stroke-width="3"/>

                        <!-- Taxa a and b connected to u -->
                        <line x1="-60" y1="0" x2="-120" y2="-30" stroke="#27ae60" stroke-width="3"/>
                        <line x1="-60" y1="0" x2="-120" y2="30" stroke="#27ae60" stroke-width="3"/>
                        <circle cx="-120" cy="-30" r="8" fill="#3498db"/>
                        <circle cx="-120" cy="30" r="8" fill="#3498db"/>

                        <!-- Remaining taxa -->
                        <line x1="0" y1="0" x2="60" y2="-40" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="0" y1="0" x2="60" y2="0" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="0" y1="0" x2="60" y2="40" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
                        <circle cx="60" cy="-40" r="8" fill="#3498db"/>
                        <circle cx="60" cy="0" r="8" fill="#3498db"/>
                        <circle cx="60" cy="40" r="8" fill="#3498db"/>

                        <!-- Labels -->
                        <text x="-130" y="-25" font-size="12" fill="#2c3e50">a</text>
                        <text x="-130" y="35" font-size="12" fill="#2c3e50">b</text>
                        <text x="70" y="-35" font-size="12" fill="#2c3e50">c</text>
                        <text x="70" y="5" font-size="12" fill="#2c3e50">d</text>
                        <text x="70" y="45" font-size="12" fill="#2c3e50">e</text>
                        <text x="-65" y="15" font-size="10" fill="#e74c3c">u</text>

                        <!-- Branch length labels -->
                        <text x="-90" y="-15" font-size="10" fill="#27ae60">2</text>
                        <text x="-90" y="20" font-size="10" fill="#27ae60">3</text>
                    </g>
                </svg>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">3</span>
                <strong>Step 3: Update Distance Matrix D₁</strong>
                <p>Calculate distances from new node u to remaining taxa c, d, e:</p>

                <div class="formula-box">
                    $$d(u,c) = \frac{1}{2}[d(a,c) + d(b,c) - d(a,b)] = \frac{1}{2}[9 + 10 - 5] = 7$$
                </div>

                <div class="matrix">
                    <table>
                        <tr>
                            <th></th><th>u</th><th>c</th><th>d</th><th>e</th>
                        </tr>
                        <tr>
                            <th>u</th><td>0</td><td>7</td><td>7</td><td>6</td>
                        </tr>
                        <tr>
                            <th>c</th><td>7</td><td>0</td><td>8</td><td>7</td>
                        </tr>
                        <tr>
                            <th>d</th><td>7</td><td>8</td><td>0</td><td style="background: #4caf50;">3</td>
                        </tr>
                        <tr>
                            <th>e</th><td>6</td><td>7</td><td style="background: #4caf50;">3</td><td>0</td>
                        </tr>
                    </table>
                </div>
            </div>

            <button class="interactive-button" onclick="showNextStep()">Continue to Step 4 →</button>

            <div id="step4" style="display: none;">
                <div class="algorithm-step">
                    <span class="step-counter">4</span>
                    <strong>Step 4: Calculate Q₂ Matrix</strong>
                    <p>With n=4 remaining taxa:</p>

                    <div class="matrix">
                        <table>
                            <tr>
                                <th></th><th>u</th><th>c</th><th>d</th><th>e</th>
                            </tr>
                            <tr>
                                <th>u</th><td>-</td><td>-28</td><td>-24</td><td>-24</td>
                            </tr>
                            <tr>
                                <th>c</th><td>-28</td><td>-</td><td>-24</td><td>-24</td>
                            </tr>
                            <tr>
                                <th>d</th><td>-24</td><td>-24</td><td>-</td><td style="background: #e74c3c; color: white;"><strong>-28</strong></td>
                            </tr>
                            <tr>
                                <th>e</th><td>-24</td><td>-24</td><td style="background: #e74c3c; color: white;"><strong>-28</strong></td><td>-</td>
                            </tr>
                        </table>
                    </div>

                    <p><strong>Result:</strong> Both Q₂(u,c) and Q₂(d,e) = -28. We can choose either pair. Let's join d and e.</p>
                </div>
            </div>

            <button class="interactive-button" onclick="showFinalSteps()" style="display: none;" id="finalButton">Show Final Steps →</button>
        </section>

        <section id="complexity" class="section">
            <h2>⚡ Complexity and Performance Analysis</h2>

            <h3>Time Complexity</h3>
            <p>The Neighbor Joining algorithm has the following complexity characteristics:</p>

            <div class="algorithm-step">
                <strong>Overall Complexity: O(n³)</strong><br>
                • Number of iterations: n-3 (where n is the number of taxa)<br>
                • Q-matrix calculation per iteration: O(n²)<br>
                • Finding minimum Q-value: O(n²)<br>
                • Distance matrix update: O(n)
            </div>

            <div class="tree-visualization">
                <h4>Complexity Breakdown Visualization</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Iteration bars -->
                    <g transform="translate(50, 50)">
                        <text x="250" y="20" font-size="16" fill="#2c3e50" text-anchor="middle">Operations per Iteration</text>

                        <!-- Iteration 1 -->
                        <rect x="0" y="40" width="100" height="30" fill="#3498db" opacity="0.8"/>
                        <text x="50" y="60" font-size="12" fill="white" text-anchor="middle">n² Q-calc</text>
                        <text x="50" y="85" font-size="10" fill="#2c3e50" text-anchor="middle">Iteration 1</text>

                        <!-- Iteration 2 -->
                        <rect x="120" y="50" width="80" height="30" fill="#3498db" opacity="0.8"/>
                        <text x="160" y="70" font-size="11" fill="white" text-anchor="middle">(n-1)² Q-calc</text>
                        <text x="160" y="95" font-size="10" fill="#2c3e50" text-anchor="middle">Iteration 2</text>

                        <!-- Iteration 3 -->
                        <rect x="220" y="60" width="60" height="30" fill="#3498db" opacity="0.8"/>
                        <text x="250" y="80" font-size="10" fill="white" text-anchor="middle">(n-2)² Q-calc</text>
                        <text x="250" y="105" font-size="10" fill="#2c3e50" text-anchor="middle">Iteration 3</text>

                        <!-- Continue pattern -->
                        <rect x="300" y="70" width="40" height="30" fill="#3498db" opacity="0.8"/>
                        <text x="320" y="90" font-size="9" fill="white" text-anchor="middle">...</text>
                        <text x="320" y="115" font-size="10" fill="#2c3e50" text-anchor="middle">...</text>

                        <!-- Final iteration -->
                        <rect x="360" y="80" width="20" height="30" fill="#3498db" opacity="0.8"/>
                        <text x="370" y="100" font-size="8" fill="white" text-anchor="middle">3²</text>
                        <text x="370" y="125" font-size="10" fill="#2c3e50" text-anchor="middle">Final</text>

                        <!-- Total complexity -->
                        <text x="200" y="160" font-size="14" fill="#e74c3c" text-anchor="middle">Total: O(n³)</text>
                        <text x="200" y="180" font-size="12" fill="#2c3e50" text-anchor="middle">Sum: n² + (n-1)² + (n-2)² + ... + 3² ≈ n³/3</text>
                    </g>

                    <!-- Performance comparison -->
                    <g transform="translate(50, 220)">
                        <text x="250" y="0" font-size="14" fill="#2c3e50" text-anchor="middle">Performance Comparison</text>

                        <!-- NJ bar -->
                        <rect x="0" y="20" width="120" height="25" fill="#27ae60"/>
                        <text x="5" y="37" font-size="11" fill="white">Neighbor Joining O(n³)</text>

                        <!-- Maximum Likelihood bar -->
                        <rect x="0" y="50" width="300" height="25" fill="#e74c3c"/>
                        <text x="5" y="67" font-size="11" fill="white">Maximum Likelihood O(n⁴⁺)</text>

                        <!-- Maximum Parsimony bar -->
                        <rect x="0" y="80" width="250" height="25" fill="#f39c12"/>
                        <text x="5" y="97" font-size="11" fill="white">Maximum Parsimony (exponential)</text>

                        <text x="320" y="67" font-size="10" fill="#2c3e50">← Much slower</text>
                    </g>
                </svg>
            </div>

            <h3>Advantages</h3>
            <div class="highlight-box">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Speed:</strong> Much faster than likelihood-based methods</li>
                    <li><strong>Accuracy:</strong> Guaranteed correct for additive distance matrices</li>
                    <li><strong>No molecular clock:</strong> Allows variable evolutionary rates</li>
                    <li><strong>Statistical consistency:</strong> Converges to true tree with sufficient data</li>
                    <li><strong>Scalability:</strong> Can handle large datasets (1000+ taxa)</li>
                </ul>
            </div>

            <h3>Disadvantages</h3>
            <div class="algorithm-step">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Distance-based:</strong> Loses information by reducing sequences to distances</li>
                    <li><strong>Negative branch lengths:</strong> Can produce biologically meaningless results</li>
                    <li><strong>Model assumptions:</strong> Assumes distances accurately reflect evolutionary relationships</li>
                    <li><strong>Superseded methods:</strong> Modern methods often more accurate</li>
                </ul>
            </div>

            <h3>Modern Implementations</h3>
            <div class="code-block">
# Example: Using Neighbor Joining in Python with BioPython
from Bio import Phylo
from Bio.Phylo.TreeConstruction import NeighborJoining, DistanceMatrix

# Create distance matrix
dm = DistanceMatrix(names=['A', 'B', 'C', 'D', 'E'],
                   matrix=[[0],
                          [5, 0],
                          [9, 10, 0],
                          [9, 10, 8, 0],
                          [8, 9, 7, 3, 0]])

# Build tree using Neighbor Joining
constructor = NeighborJoining()
tree = constructor.build_tree(dm)

# Display tree
Phylo.draw_ascii(tree)
            </div>

            <div class="tree-visualization">
                <h4>Final Phylogenetic Tree</h4>
                <svg width="500" height="250" viewBox="0 0 500 250">
                    <g transform="translate(250, 125)">
                        <!-- Main tree structure -->
                        <line x1="0" y1="0" x2="-80" y2="-40" stroke="#27ae60" stroke-width="4"/>
                        <line x1="0" y1="0" x2="-80" y2="40" stroke="#27ae60" stroke-width="4"/>
                        <line x1="0" y1="0" x2="100" y2="0" stroke="#27ae60" stroke-width="4"/>

                        <!-- Left subtree (a,b joined to u) -->
                        <line x1="-80" y1="-40" x2="-140" y2="-60" stroke="#27ae60" stroke-width="3"/>
                        <line x1="-80" y1="-40" x2="-140" y2="-20" stroke="#27ae60" stroke-width="3"/>

                        <!-- Right subtree (c,d joined to v) -->
                        <line x1="-80" y1="40" x2="-140" y2="20" stroke="#27ae60" stroke-width="3"/>
                        <line x1="-80" y1="40" x2="-140" y2="60" stroke="#27ae60" stroke-width="3"/>

                        <!-- Internal nodes -->
                        <circle cx="0" cy="0" r="6" fill="#e74c3c"/>
                        <circle cx="-80" cy="-40" r="5" fill="#e74c3c"/>
                        <circle cx="-80" cy="40" r="5" fill="#e74c3c"/>

                        <!-- Terminal nodes -->
                        <circle cx="-140" cy="-60" r="8" fill="#3498db"/>
                        <circle cx="-140" cy="-20" r="8" fill="#3498db"/>
                        <circle cx="-140" cy="20" r="8" fill="#3498db"/>
                        <circle cx="-140" cy="60" r="8" fill="#3498db"/>
                        <circle cx="100" cy="0" r="8" fill="#3498db"/>

                        <!-- Labels -->
                        <text x="-150" y="-55" font-size="14" fill="#2c3e50" font-weight="bold">a</text>
                        <text x="-150" y="-15" font-size="14" fill="#2c3e50" font-weight="bold">b</text>
                        <text x="-150" y="25" font-size="14" fill="#2c3e50" font-weight="bold">c</text>
                        <text x="-150" y="65" font-size="14" fill="#2c3e50" font-weight="bold">d</text>
                        <text x="110" y="5" font-size="14" fill="#2c3e50" font-weight="bold">e</text>

                        <!-- Branch length labels -->
                        <text x="-110" y="-45" font-size="10" fill="#27ae60" font-weight="bold">2</text>
                        <text x="-110" y="-10" font-size="10" fill="#27ae60" font-weight="bold">3</text>
                        <text x="-110" y="30" font-size="10" fill="#27ae60" font-weight="bold">4</text>
                        <text x="-110" y="50" font-size="10" fill="#27ae60" font-weight="bold">2</text>
                        <text x="50" y="10" font-size="10" fill="#27ae60" font-weight="bold">1</text>
                        <text x="-40" y="-20" font-size="10" fill="#27ae60" font-weight="bold">3</text>
                        <text x="-40" y="30" font-size="10" fill="#27ae60" font-weight="bold">2</text>
                        <text x="20" y="10" font-size="10" fill="#27ae60" font-weight="bold">2</text>

                        <text x="0" y="100" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                            Complete Neighbor Joining Tree
                        </text>
                        <text x="0" y="120" font-size="12" fill="#2c3e50" text-anchor="middle">
                            Branch lengths represent evolutionary distances
                        </text>
                    </g>
                </svg>
            </div>
        </section>
    </div>

    <script>
        // Add smooth scrolling for navigation
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Interactive example functions
        function showNextStep() {
            document.getElementById('step4').style.display = 'block';
            document.getElementById('finalButton').style.display = 'inline-block';
            event.target.style.display = 'none';
        }

        function showFinalSteps() {
            const finalStepsHTML = `
                <div class="algorithm-step">
                    <span class="step-counter">5</span>
                    <strong>Step 5: Join d and e, Calculate Final Tree</strong>
                    <p>Create new internal node v connecting d and e, then connect remaining taxa:</p>

                    <div class="formula-box">
                        $$\\delta(d,v) = \\frac{1}{2} \\times 3 + \\frac{1}{2(4-2)}[7 - 6] = 1.5 + 0.25 = 2$$
                    </div>

                    <div class="formula-box">
                        $$\\delta(e,v) = 3 - 2 = 1$$
                    </div>
                </div>

                <div class="tree-visualization">
                    <h4>Final Complete Tree</h4>
                    <svg width="500" height="200" viewBox="0 0 500 200">
                        <g transform="translate(250, 100)">
                            <!-- Complete tree structure -->
                            <line x1="0" y1="0" x2="-60" y2="-30" stroke="#27ae60" stroke-width="3"/>
                            <line x1="0" y1="0" x2="-60" y2="30" stroke="#27ae60" stroke-width="3"/>
                            <line x1="0" y1="0" x2="80" y2="0" stroke="#27ae60" stroke-width="3"/>

                            <!-- Left subtree -->
                            <line x1="-60" y1="-30" x2="-120" y2="-50" stroke="#27ae60" stroke-width="2"/>
                            <line x1="-60" y1="-30" x2="-120" y2="-10" stroke="#27ae60" stroke-width="2"/>

                            <!-- Right subtree -->
                            <line x1="-60" y1="30" x2="-120" y2="10" stroke="#27ae60" stroke-width="2"/>
                            <line x1="-60" y1="30" x2="-120" y2="50" stroke="#27ae60" stroke-width="2"/>

                            <!-- Nodes -->
                            <circle cx="0" cy="0" r="5" fill="#e74c3c"/>
                            <circle cx="-60" cy="-30" r="4" fill="#e74c3c"/>
                            <circle cx="-60" cy="30" r="4" fill="#e74c3c"/>
                            <circle cx="80" cy="0" r="8" fill="#3498db"/>
                            <circle cx="-120" cy="-50" r="8" fill="#3498db"/>
                            <circle cx="-120" cy="-10" r="8" fill="#3498db"/>
                            <circle cx="-120" cy="10" r="8" fill="#3498db"/>
                            <circle cx="-120" cy="50" r="8" fill="#3498db"/>

                            <!-- Labels -->
                            <text x="90" y="5" font-size="12" fill="#2c3e50" font-weight="bold">e</text>
                            <text x="-130" y="-45" font-size="12" fill="#2c3e50" font-weight="bold">a</text>
                            <text x="-130" y="-5" font-size="12" fill="#2c3e50" font-weight="bold">b</text>
                            <text x="-130" y="15" font-size="12" fill="#2c3e50" font-weight="bold">c</text>
                            <text x="-130" y="55" font-size="12" fill="#2c3e50" font-weight="bold">d</text>

                            <!-- Branch lengths -->
                            <text x="-90" y="-35" font-size="10" fill="#27ae60">2</text>
                            <text x="-90" y="-5" font-size="10" fill="#27ae60">3</text>
                            <text x="-90" y="20" font-size="10" fill="#27ae60">4</text>
                            <text x="-90" y="40" font-size="10" fill="#27ae60">2</text>
                            <text x="40" y="10" font-size="10" fill="#27ae60">1</text>
                            <text x="-30" y="-15" font-size="10" fill="#27ae60">3</text>
                            <text x="-30" y="20" font-size="10" fill="#27ae60">2</text>
                            <text x="20" y="10" font-size="10" fill="#27ae60">2</text>
                        </g>
                    </svg>
                </div>

                <div class="highlight-box">
                    <strong>🎉 Algorithm Complete!</strong><br>
                    The Neighbor Joining algorithm has successfully constructed a phylogenetic tree showing the evolutionary relationships between taxa a, b, c, d, and e. Notice how the tree reflects the original distance matrix: closely related taxa (like d and e with distance 3) are grouped together with short branch lengths.
                </div>
            `;

            const container = document.getElementById('example');
            container.innerHTML += finalStepsHTML;
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([container]).catch((err) => console.log(err.message));
            }
        }

        // Add interactive highlighting for matrices
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to matrix cells
            const matrixCells = document.querySelectorAll('td');
            matrixCells.forEach(cell => {
                cell.addEventListener('mouseenter', function() {
                    if (this.style.background && this.style.background !== 'white') {
                        this.style.transform = 'scale(1.1)';
                        this.style.transition = 'transform 0.2s';
                    }
                });

                cell.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Add progress indicator
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');

            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });
        });

        // Add CSS for active navigation
        const style = document.createElement('style');
        style.textContent = `
            .navigation a.active {
                color: #4facfe !important;
                font-weight: bold;
            }

            .navigation a.active::before {
                content: "→ ";
                color: #4facfe;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
