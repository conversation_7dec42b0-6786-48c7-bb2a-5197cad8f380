<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageRank Algorithm Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
            color: white;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #4285f4;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 2px solid #4285f4;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }
        
        .algorithm-step {
            background: #e8f4fd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .formula-box {
            background: #f1f2f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .matrix {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        table {
            border-collapse: collapse;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-counter {
            background: #4285f4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        
        .navigation ul {
            list-style: none;
        }
        
        .navigation li {
            margin: 5px 0;
        }
        
        .navigation a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .navigation a:hover {
            color: #4285f4;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(66, 133, 244, 0.4);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .google-colors {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc05 50%, #ea4335 75%, #4285f4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }
        
        .page-node {
            transition: all 0.3s ease;
        }
        
        .page-node:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="navigation">
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#concepts">Core Concepts</a></li>
            <li><a href="#mathematics">Mathematics</a></li>
            <li><a href="#algorithm">Algorithm</a></li>
            <li><a href="#example">Example</a></li>
            <li><a href="#implementation">Implementation</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="google-colors">PageRank Algorithm</h1>
            <p>The Foundation of Google's Search Engine</p>
        </div>

        <section id="introduction" class="section">
            <h2>🌐 Introduction to PageRank</h2>
            
            <div class="highlight-box">
                <strong>What is PageRank?</strong><br>
                PageRank is an algorithm used by Google Search to rank web pages in their search engine results. It measures the importance of website pages by analyzing the link structure of the web.
            </div>

            <h3>Historical Context</h3>
            <p>PageRank was developed by Larry Page and Sergey Brin at Stanford University in 1996. Named after Larry Page (and the concept of web pages), it became the foundation of Google's search engine.</p>

            <div class="algorithm-step">
                <strong>🎯 Core Principle:</strong><br>
                "More important websites are likely to receive more links from other websites."<br><br>
                The algorithm counts both the <em>number</em> and <em>quality</em> of links to determine a page's importance.
            </div>

            <h3>The Random Surfer Model</h3>
            <p>PageRank models a hypothetical "random surfer" who:</p>
            
            <ul style="margin: 20px 0; padding-left: 30px;">
                <li>Starts at a random web page</li>
                <li>Follows links randomly with probability d (damping factor ≈ 0.85)</li>
                <li>Jumps to a completely random page with probability (1-d)</li>
                <li>Continues this process indefinitely</li>
            </ul>

            <div class="visualization">
                <h4>Random Surfer Visualization</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Web pages -->
                    <circle cx="150" cy="100" r="30" fill="#4285f4" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                    <text x="150" y="105" font-size="14" fill="white" text-anchor="middle" font-weight="bold">A</text>
                    
                    <circle cx="450" cy="100" r="30" fill="#34a853" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                    <text x="450" y="105" font-size="14" fill="white" text-anchor="middle" font-weight="bold">B</text>
                    
                    <circle cx="150" cy="220" r="30" fill="#fbbc05" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                    <text x="150" y="225" font-size="14" fill="white" text-anchor="middle" font-weight="bold">C</text>
                    
                    <circle cx="450" cy="220" r="30" fill="#ea4335" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                    <text x="450" y="225" font-size="14" fill="white" text-anchor="middle" font-weight="bold">D</text>
                    
                    <!-- Links between pages -->
                    <path d="M 180 100 L 420 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 450 130 L 450 190" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 420 220 L 180 220" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 150 190 L 150 130" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 180 220 L 420 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    
                    <!-- Random surfer -->
                    <circle cx="300" cy="160" r="8" fill="#e74c3c">
                        <animateMotion dur="4s" repeatCount="indefinite">
                            <path d="M 0,0 L 150,-60 L 0,60 L -150,-60 L 0,0"/>
                        </animateMotion>
                    </circle>
                    <text x="320" y="165" font-size="12" fill="#e74c3c" font-weight="bold">Random Surfer</text>
                    
                    <!-- Random jump illustration -->
                    <path d="M 300 50 Q 350 25 400 50" stroke="#e74c3c" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                    <text x="350" y="20" font-size="10" fill="#e74c3c" text-anchor="middle">Random Jump (1-d)</text>
                    
                    <!-- Legend -->
                    <text x="300" y="280" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                        PageRank = Probability of Random Surfer Landing on Page
                    </text>
                    
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </section>

        <section id="concepts" class="section">
            <h2>💡 Core Algorithm Concepts</h2>

            <h3>Link Analysis Principles</h3>
            <p>PageRank is fundamentally based on the idea that links represent votes of confidence:</p>

            <div class="algorithm-step">
                <strong>🗳️ Link as Vote:</strong><br>
                When page A links to page B, it's like page A "voting" for page B's importance.
            </div>

            <div class="algorithm-step">
                <strong>⚖️ Weighted Voting:</strong><br>
                A vote from a high-PageRank page is worth more than a vote from a low-PageRank page.
            </div>

            <div class="algorithm-step">
                <strong>📊 Vote Distribution:</strong><br>
                A page's voting power is divided equally among all pages it links to.
            </div>

            <h3>The Damping Factor</h3>
            <div class="highlight-box">
                <strong>Why Damping Factor (d ≈ 0.85)?</strong><br>
                The damping factor represents the probability that a random surfer will continue clicking links rather than jumping to a random page. It prevents certain mathematical problems and models real user behavior.
            </div>

            <div class="visualization">
                <h4>Link Analysis Visualization</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Example web graph -->
                    <g transform="translate(350, 200)">
                        <!-- Page A (high authority) -->
                        <circle cx="-200" cy="-100" r="40" fill="#4285f4" stroke="#2c3e50" stroke-width="3" class="page-node"/>
                        <text x="-200" y="-95" font-size="16" fill="white" text-anchor="middle" font-weight="bold">A</text>
                        <text x="-200" y="-65" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.4</text>

                        <!-- Page B -->
                        <circle cx="200" cy="-100" r="25" fill="#34a853" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                        <text x="200" y="-95" font-size="14" fill="white" text-anchor="middle" font-weight="bold">B</text>
                        <text x="200" y="-70" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.2</text>

                        <!-- Page C -->
                        <circle cx="-100" cy="80" r="35" fill="#fbbc05" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                        <text x="-100" y="85" font-size="14" fill="white" text-anchor="middle" font-weight="bold">C</text>
                        <text x="-100" y="125" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.3</text>

                        <!-- Page D -->
                        <circle cx="100" cy="80" r="20" fill="#ea4335" stroke="#2c3e50" stroke-width="2" class="page-node"/>
                        <text x="100" y="85" font-size="14" fill="white" text-anchor="middle" font-weight="bold">D</text>
                        <text x="100" y="110" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.1</text>

                        <!-- Links with vote values -->
                        <!-- A to B -->
                        <path d="M -160 -100 L 160 -100" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#greenArrow)"/>
                        <text x="0" y="-85" font-size="12" fill="#27ae60" text-anchor="middle" font-weight="bold">Vote: 0.4</text>

                        <!-- A to C -->
                        <path d="M -180 -70 L -120 50" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#greenArrow)"/>
                        <text x="-160" y="-10" font-size="12" fill="#27ae60" text-anchor="middle" font-weight="bold">Vote: 0.4</text>

                        <!-- B to D -->
                        <path d="M 190 -75 L 110 55" stroke="#f39c12" stroke-width="2" fill="none" marker-end="url(#orangeArrow)"/>
                        <text x="160" y="-10" font-size="12" fill="#f39c12" text-anchor="middle" font-weight="bold">Vote: 0.2</text>

                        <!-- C to A -->
                        <path d="M -120 50 L -180 -70" stroke="#e67e22" stroke-width="2" fill="none" marker-end="url(#orangeArrow)"/>
                        <text x="-140" y="-10" font-size="12" fill="#e67e22" text-anchor="middle" font-weight="bold">Vote: 0.3</text>

                        <!-- Multiple links to show vote splitting -->
                        <text x="-250" y="-50" font-size="11" fill="#2c3e50" text-anchor="middle">A has 2 outlinks</text>
                        <text x="-250" y="-35" font-size="11" fill="#2c3e50" text-anchor="middle">Each gets 0.4/2 = 0.2</text>

                        <!-- Importance indicators -->
                        <text x="0" y="160" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                            Page Importance ∝ Circle Size
                        </text>

                        <text x="0" y="180" font-size="12" fill="#2c3e50" text-anchor="middle">
                            Vote Value = Voter's PageRank ÷ Number of Outlinks
                        </text>
                    </g>

                    <defs>
                        <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                        </marker>
                        <marker id="orangeArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Key Properties</h3>

            <div class="algorithm-step">
                <strong>🔄 Recursive Definition:</strong><br>
                A page's PageRank depends on the PageRank of pages linking to it.
            </div>

            <div class="algorithm-step">
                <strong>📈 Convergence:</strong><br>
                The algorithm iteratively updates PageRank values until they stabilize.
            </div>

            <div class="algorithm-step">
                <strong>🎲 Probabilistic Interpretation:</strong><br>
                PageRank represents the probability that a random surfer lands on a particular page.
            </div>

            <div class="highlight-box">
                <strong>Real-World Impact:</strong><br>
                PageRank revolutionized web search by providing an objective measure of page importance based on the web's link structure, rather than just content analysis. It became the foundation for Google's dominance in search.
            </div>
        </section>

        <section id="mathematics" class="section">
            <h2>📐 Mathematical Foundation</h2>

            <h3>Basic PageRank Formula</h3>
            <p>The fundamental PageRank equation for a page u is:</p>

            <div class="formula-box">
                $$PR(u) = \sum_{v \in B_u} \frac{PR(v)}{L(v)}$$
            </div>

            <div class="algorithm-step">
                <strong>Formula Components:</strong><br>
                • $PR(u)$: PageRank of page u<br>
                • $B_u$: Set of pages that link to page u<br>
                • $PR(v)$: PageRank of page v (linking to u)<br>
                • $L(v)$: Number of outbound links from page v
            </div>

            <h3>PageRank with Damping Factor</h3>
            <p>The complete PageRank formula includes the damping factor:</p>

            <div class="formula-box">
                $$PR(u) = \frac{1-d}{N} + d \sum_{v \in B_u} \frac{PR(v)}{L(v)}$$
            </div>

            <div class="highlight-box">
                <strong>Understanding the Damping Factor:</strong><br>
                • $\frac{1-d}{N}$: Probability of random jump to page u<br>
                • $d$: Damping factor (typically 0.85)<br>
                • $N$: Total number of pages<br>
                • The formula balances link-based importance with random exploration
            </div>

            <h3>Matrix Formulation</h3>
            <p>PageRank can be expressed as an eigenvector problem:</p>

            <div class="formula-box">
                $$\mathbf{R} = d\mathcal{M}\mathbf{R} + \frac{1-d}{N}\mathbf{1}$$
            </div>

            <div class="algorithm-step">
                <strong>Matrix Components:</strong><br>
                • $\mathbf{R}$: PageRank vector<br>
                • $\mathcal{M}$: Transition matrix where $\mathcal{M}_{ij} = \frac{1}{L(p_j)}$ if j links to i<br>
                • $\mathbf{1}$: Vector of all ones<br>
                • This is equivalent to finding the principal eigenvector of $\hat{\mathcal{M}} = d\mathcal{M} + \frac{1-d}{N}\mathbf{E}$
            </div>

            <div class="visualization">
                <h4>Mathematical Visualization: Vote Distribution</h4>
                <svg width="600" height="350" viewBox="0 0 600 350">
                    <!-- Central page receiving votes -->
                    <circle cx="300" cy="175" r="40" fill="#4285f4" stroke="#2c3e50" stroke-width="3"/>
                    <text x="300" y="180" font-size="16" fill="white" text-anchor="middle" font-weight="bold">Page A</text>
                    <text x="300" y="230" font-size="12" fill="#2c3e50" text-anchor="middle">Receiving Votes</text>

                    <!-- Voting pages -->
                    <circle cx="100" cy="80" r="25" fill="#34a853" stroke="#2c3e50" stroke-width="2"/>
                    <text x="100" y="85" font-size="12" fill="white" text-anchor="middle" font-weight="bold">B</text>
                    <text x="100" y="50" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.3</text>
                    <text x="100" y="115" font-size="10" fill="#2c3e50" text-anchor="middle">2 outlinks</text>

                    <circle cx="500" cy="80" r="30" fill="#fbbc05" stroke="#2c3e50" stroke-width="2"/>
                    <text x="500" y="85" font-size="12" fill="white" text-anchor="middle" font-weight="bold">C</text>
                    <text x="500" y="50" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.4</text>
                    <text x="500" y="115" font-size="10" fill="#2c3e50" text-anchor="middle">1 outlink</text>

                    <circle cx="300" cy="80" r="20" fill="#ea4335" stroke="#2c3e50" stroke-width="2"/>
                    <text x="300" y="85" font-size="12" fill="white" text-anchor="middle" font-weight="bold">D</text>
                    <text x="300" y="50" font-size="10" fill="#2c3e50" text-anchor="middle">PR = 0.2</text>
                    <text x="300" y="115" font-size="10" fill="#2c3e50" text-anchor="middle">4 outlinks</text>

                    <!-- Vote arrows with calculations -->
                    <path d="M 120 100 L 270 155" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#voteArrow)"/>
                    <text x="190" y="125" font-size="11" fill="#27ae60" text-anchor="middle" font-weight="bold">0.3/2 = 0.15</text>

                    <path d="M 480 100 L 330 155" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#voteArrow)"/>
                    <text x="410" y="125" font-size="11" fill="#27ae60" text-anchor="middle" font-weight="bold">0.4/1 = 0.4</text>

                    <path d="M 300 100 L 300 135" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#voteArrow)"/>
                    <text x="320" y="120" font-size="11" fill="#27ae60" text-anchor="middle" font-weight="bold">0.2/4 = 0.05</text>

                    <!-- Calculation box -->
                    <rect x="50" y="250" width="500" height="80" fill="#f8f9fa" stroke="#ddd" stroke-width="2" rx="10"/>
                    <text x="300" y="275" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">PageRank Calculation for Page A</text>

                    <text x="70" y="300" font-size="12" fill="#2c3e50">PR(A) = (1-d)/N + d × [PR(B)/L(B) + PR(C)/L(C) + PR(D)/L(D)]</text>
                    <text x="70" y="320" font-size="12" fill="#2c3e50">PR(A) = (1-0.85)/4 + 0.85 × [0.15 + 0.4 + 0.05] = 0.0375 + 0.51 = 0.5475</text>

                    <!-- Random jump illustration -->
                    <path d="M 50 175 Q 25 175 50 175" stroke="#e74c3c" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                    <text x="25" y="170" font-size="10" fill="#e74c3c" text-anchor="middle">Random</text>
                    <text x="25" y="185" font-size="10" fill="#e74c3c" text-anchor="middle">Jump</text>
                    <text x="25" y="200" font-size="10" fill="#e74c3c" text-anchor="middle">(1-d)/N</text>

                    <defs>
                        <marker id="voteArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Iterative Update Formula</h3>
            <p>The algorithm updates PageRank values iteratively:</p>

            <div class="formula-box">
                $$PR^{(t+1)}(u) = \frac{1-d}{N} + d \sum_{v \in B_u} \frac{PR^{(t)}(v)}{L(v)}$$
            </div>

            <div class="algorithm-step">
                <strong>Convergence Condition:</strong><br>
                The algorithm stops when the change between iterations is small:
                <div class="formula-box">
                    $$|\mathbf{R}^{(t+1)} - \mathbf{R}^{(t)}| < \epsilon$$
                </div>
                where $\epsilon$ is a small tolerance value (e.g., $10^{-6}$).
            </div>

            <h3>Power Method Interpretation</h3>
            <div class="highlight-box">
                <strong>Eigenvector Connection:</strong><br>
                PageRank is the principal eigenvector of the Google matrix $\hat{\mathcal{M}}$. The power method iteratively applies this matrix until convergence, which is guaranteed for the specific structure of web graphs.
            </div>
        </section>

        <section id="algorithm" class="section">
            <h2>⚙️ Step-by-Step Algorithm</h2>

            <p>Let's break down the PageRank algorithm into clear, executable steps:</p>

            <div class="algorithm-step">
                <span class="step-counter">1</span>
                <strong>Initialize PageRank Values</strong>
                <p>Set initial PageRank for all pages to equal values:</p>
                <div class="formula-box">
                    $$PR^{(0)}(p_i) = \frac{1}{N} \quad \forall i \in \{1, 2, ..., N\}$$
                </div>
                <p>Where N is the total number of pages in the graph.</p>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">2</span>
                <strong>Build Transition Matrix</strong>
                <p>Create the link matrix $\mathcal{M}$ where:</p>
                <div class="formula-box">
                    $$\mathcal{M}_{ij} = \begin{cases}
                    \frac{1}{L(p_j)} & \text{if page j links to page i} \\
                    0 & \text{otherwise}
                    \end{cases}$$
                </div>
                <p>Handle dangling nodes (pages with no outlinks) by connecting them to all pages.</p>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">3</span>
                <strong>Iterative Update</strong>
                <p>For each iteration t, update PageRank values:</p>
                <div class="formula-box">
                    $$\mathbf{R}^{(t+1)} = d\mathcal{M}\mathbf{R}^{(t)} + \frac{1-d}{N}\mathbf{1}$$
                </div>
                <p>This can be computed element-wise as:</p>
                <div class="formula-box">
                    $$PR^{(t+1)}(u) = \frac{1-d}{N} + d \sum_{v \in B_u} \frac{PR^{(t)}(v)}{L(v)}$$
                </div>
            </div>

            <div class="algorithm-step">
                <span class="step-counter">4</span>
                <strong>Check Convergence</strong>
                <p>Continue iterations until convergence:</p>
                <div class="formula-box">
                    $$\|\mathbf{R}^{(t+1)} - \mathbf{R}^{(t)}\|_1 < \epsilon$$
                </div>
                <p>Typically, $\epsilon = 10^{-6}$ and convergence occurs in 50-100 iterations.</p>
            </div>

            <div class="visualization">
                <h4>Algorithm Flow Visualization</h4>
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Flow chart -->
                    <g transform="translate(350, 50)">
                        <!-- Start -->
                        <ellipse cx="0" cy="0" rx="60" ry="25" fill="#4285f4" stroke="#2c3e50" stroke-width="2"/>
                        <text x="0" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">START</text>

                        <!-- Initialize -->
                        <rect x="-80" y="50" width="160" height="40" fill="#34a853" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="75" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Initialize: PR(i) = 1/N</text>

                        <!-- Build matrix -->
                        <rect x="-80" y="120" width="160" height="40" fill="#fbbc05" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="145" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Build Transition Matrix M</text>

                        <!-- Iteration loop -->
                        <rect x="-100" y="190" width="200" height="40" fill="#ea4335" stroke="#2c3e50" stroke-width="2" rx="5"/>
                        <text x="0" y="215" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Update: R = dMR + (1-d)/N</text>

                        <!-- Convergence check -->
                        <polygon points="-60,260 60,260 80,290 60,320 -60,320 -80,290" fill="#9c27b0" stroke="#2c3e50" stroke-width="2"/>
                        <text x="0" y="290" font-size="11" fill="white" text-anchor="middle" font-weight="bold">Converged?</text>

                        <!-- End -->
                        <ellipse cx="0" cy="370" rx="60" ry="25" fill="#607d8b" stroke="#2c3e50" stroke-width="2"/>
                        <text x="0" y="375" font-size="12" fill="white" text-anchor="middle" font-weight="bold">END</text>

                        <!-- Arrows -->
                        <path d="M 0 25 L 0 50" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#flowArrow)"/>
                        <path d="M 0 90 L 0 120" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#flowArrow)"/>
                        <path d="M 0 160 L 0 190" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#flowArrow)"/>
                        <path d="M 0 230 L 0 260" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#flowArrow)"/>
                        <path d="M 0 320 L 0 345" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#flowArrow)"/>

                        <!-- Loop back arrow -->
                        <path d="M -80 290 Q -150 290 -150 210 L -100 210" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#flowArrow)"/>
                        <text x="-120" y="250" font-size="10" fill="#e74c3c" text-anchor="middle">NO</text>

                        <!-- Yes arrow -->
                        <text x="40" y="340" font-size="10" fill="#27ae60" text-anchor="middle">YES</text>

                        <!-- Iteration counter -->
                        <text x="150" y="215" font-size="10" fill="#2c3e50">t = t + 1</text>

                        <!-- Convergence formula -->
                        <text x="120" y="290" font-size="9" fill="#2c3e50">||R^(t+1) - R^(t)|| < ε</text>
                    </g>

                    <defs>
                        <marker id="flowArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Handling Special Cases</h3>

            <div class="algorithm-step">
                <strong>🕳️ Dangling Nodes (No Outlinks):</strong><br>
                Pages with no outbound links are connected to all pages with equal probability $\frac{1}{N}$.
            </div>

            <div class="algorithm-step">
                <strong>🏝️ Disconnected Components:</strong><br>
                The damping factor ensures the graph is strongly connected, preventing rank sinks.
            </div>

            <div class="algorithm-step">
                <strong>🔄 Self-Links:</strong><br>
                Links from a page to itself are typically ignored in PageRank calculations.
            </div>

            <div class="highlight-box">
                <strong>Convergence Guarantee:</strong><br>
                The PageRank algorithm is guaranteed to converge because the Google matrix $\hat{\mathcal{M}}$ is:
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Stochastic:</strong> Each column sums to 1</li>
                    <li><strong>Irreducible:</strong> Every page can reach every other page</li>
                    <li><strong>Aperiodic:</strong> The damping factor prevents cycles</li>
                </ul>
                These properties ensure a unique principal eigenvector exists.
            </div>
        </section>

        <section id="example" class="section">
            <h2>🔍 Detailed Example: 4-Page Web Graph</h2>

            <p>Let's work through a complete example with 4 web pages (A, B, C, D) to see how PageRank evolves through iterations.</p>

            <h3>Initial Setup</h3>
            <div class="highlight-box">
                <strong>Web Graph Structure:</strong><br>
                • Page B links to pages A and C<br>
                • Page C links to page A<br>
                • Page D links to pages A, B, and C<br>
                • Page A has no outbound links (dangling node)
            </div>

            <div class="visualization">
                <h4>Web Graph Structure</h4>
                <svg width="500" height="300" viewBox="0 0 500 300">
                    <!-- Pages -->
                    <circle cx="100" cy="100" r="35" fill="#4285f4" stroke="#2c3e50" stroke-width="3" class="page-node"/>
                    <text x="100" y="105" font-size="16" fill="white" text-anchor="middle" font-weight="bold">A</text>

                    <circle cx="400" cy="100" r="35" fill="#34a853" stroke="#2c3e50" stroke-width="3" class="page-node"/>
                    <text x="400" y="105" font-size="16" fill="white" text-anchor="middle" font-weight="bold">B</text>

                    <circle cx="100" cy="220" r="35" fill="#fbbc05" stroke="#2c3e50" stroke-width="3" class="page-node"/>
                    <text x="100" y="225" font-size="16" fill="white" text-anchor="middle" font-weight="bold">C</text>

                    <circle cx="400" cy="220" r="35" fill="#ea4335" stroke="#2c3e50" stroke-width="3" class="page-node"/>
                    <text x="400" y="225" font-size="16" fill="white" text-anchor="middle" font-weight="bold">D</text>

                    <!-- Links -->
                    <!-- B to A -->
                    <path d="M 365 100 L 135 100" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#linkArrow)"/>
                    <text x="250" y="90" font-size="11" fill="#27ae60" text-anchor="middle" font-weight="bold">B→A</text>

                    <!-- B to C -->
                    <path d="M 380 130 L 120 200" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#linkArrow)"/>
                    <text x="250" y="170" font-size="11" fill="#27ae60" text-anchor="middle" font-weight="bold">B→C</text>

                    <!-- C to A -->
                    <path d="M 100 185 L 100 135" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#linkArrow)"/>
                    <text x="80" y="160" font-size="11" fill="#f39c12" text-anchor="middle" font-weight="bold">C→A</text>

                    <!-- D to A -->
                    <path d="M 380 190 L 120 130" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#linkArrow)"/>
                    <text x="280" y="150" font-size="11" fill="#e74c3c" text-anchor="middle" font-weight="bold">D→A</text>

                    <!-- D to B -->
                    <path d="M 400 185 L 400 135" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#linkArrow)"/>
                    <text x="420" y="160" font-size="11" fill="#e74c3c" text-anchor="middle" font-weight="bold">D→B</text>

                    <!-- D to C -->
                    <path d="M 365 220 L 135 220" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#linkArrow)"/>
                    <text x="250" y="240" font-size="11" fill="#e74c3c" text-anchor="middle" font-weight="bold">D→C</text>

                    <!-- Outlink counts -->
                    <text x="100" y="70" font-size="10" fill="#2c3e50" text-anchor="middle">0 outlinks</text>
                    <text x="400" y="70" font-size="10" fill="#2c3e50" text-anchor="middle">2 outlinks</text>
                    <text x="100" y="250" font-size="10" fill="#2c3e50" text-anchor="middle">1 outlink</text>
                    <text x="400" y="250" font-size="10" fill="#2c3e50" text-anchor="middle">3 outlinks</text>

                    <defs>
                        <marker id="linkArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Transition Matrix Construction</h3>
            <div class="matrix">
                <h4>Transition Matrix M</h4>
                <table>
                    <tr><th></th><th>A</th><th>B</th><th>C</th><th>D</th></tr>
                    <tr><th>A</th><td style="background: #ffeb3b;">0.25</td><td>0.5</td><td>1</td><td>1/3</td></tr>
                    <tr><th>B</th><td style="background: #ffeb3b;">0.25</td><td>0</td><td>0</td><td>1/3</td></tr>
                    <tr><th>C</th><td style="background: #ffeb3b;">0.25</td><td>0.5</td><td>0</td><td>1/3</td></tr>
                    <tr><th>D</th><td style="background: #ffeb3b;">0.25</td><td>0</td><td>0</td><td>0</td></tr>
                </table>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    Note: Column A shows equal distribution (0.25) because A is a dangling node
                </p>
            </div>

            <button class="interactive-button" onclick="showIterations()">Start PageRank Iterations →</button>

            <div id="iterations" style="display: none;">
                <h3>Iteration Results</h3>

                <div class="algorithm-step">
                    <strong>Initial Values (t=0):</strong>
                    <div class="matrix">
                        <table>
                            <tr><th>Page</th><th>A</th><th>B</th><th>C</th><th>D</th></tr>
                            <tr><th>PageRank</th><td>0.25</td><td>0.25</td><td>0.25</td><td>0.25</td></tr>
                        </table>
                    </div>
                </div>

                <div class="algorithm-step">
                    <strong>After Iteration 1:</strong>
                    <div class="formula-box">
                        $$PR^{(1)}(A) = \frac{1-0.85}{4} + 0.85 \times \left(\frac{0.25}{2} + \frac{0.25}{1} + \frac{0.25}{3}\right) = 0.0375 + 0.85 \times 0.458 = 0.427$$
                    </div>
                    <div class="matrix">
                        <table>
                            <tr><th>Page</th><th>A</th><th>B</th><th>C</th><th>D</th></tr>
                            <tr><th>PageRank</th><td style="background: #4caf50;">0.427</td><td>0.108</td><td>0.321</td><td>0.144</td></tr>
                        </table>
                    </div>
                </div>

                <button class="interactive-button" onclick="showConvergence()">Show Convergence →</button>
            </div>

            <div id="convergence" style="display: none;">
                <div class="visualization">
                    <h4>PageRank Convergence</h4>
                    <svg width="600" height="300" viewBox="0 0 600 300">
                        <!-- Convergence graph -->
                        <g transform="translate(50, 50)">
                            <!-- Axes -->
                            <line x1="0" y1="200" x2="500" y2="200" stroke="#2c3e50" stroke-width="2"/>
                            <line x1="0" y1="0" x2="0" y2="200" stroke="#2c3e50" stroke-width="2"/>

                            <!-- Axis labels -->
                            <text x="250" y="230" font-size="12" fill="#2c3e50" text-anchor="middle">Iteration</text>
                            <text x="-30" y="100" font-size="12" fill="#2c3e50" text-anchor="middle" transform="rotate(-90 -30 100)">PageRank</text>

                            <!-- Grid lines -->
                            <line x1="0" y1="160" x2="500" y2="160" stroke="#ddd" stroke-width="1"/>
                            <line x1="0" y1="120" x2="500" y2="120" stroke="#ddd" stroke-width="1"/>
                            <line x1="0" y1="80" x2="500" y2="80" stroke="#ddd" stroke-width="1"/>
                            <line x1="0" y1="40" x2="500" y2="40" stroke="#ddd" stroke-width="1"/>

                            <!-- Iteration markers -->
                            <text x="50" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">0</text>
                            <text x="150" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">5</text>
                            <text x="250" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">10</text>
                            <text x="350" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">15</text>
                            <text x="450" y="215" font-size="10" fill="#2c3e50" text-anchor="middle">20</text>

                            <!-- PageRank value markers -->
                            <text x="-15" y="205" font-size="10" fill="#2c3e50" text-anchor="middle">0</text>
                            <text x="-15" y="165" font-size="10" fill="#2c3e50" text-anchor="middle">0.2</text>
                            <text x="-15" y="125" font-size="10" fill="#2c3e50" text-anchor="middle">0.4</text>
                            <text x="-15" y="85" font-size="10" fill="#2c3e50" text-anchor="middle">0.6</text>
                            <text x="-15" y="45" font-size="10" fill="#2c3e50" text-anchor="middle">0.8</text>

                            <!-- Convergence curves -->
                            <!-- Page A (highest) -->
                            <path d="M 50 160 Q 150 100 250 80 Q 350 75 450 75" stroke="#4285f4" stroke-width="3" fill="none"/>
                            <circle cx="450" cy="75" r="4" fill="#4285f4"/>
                            <text x="470" y="80" font-size="11" fill="#4285f4" font-weight="bold">A: 0.387</text>

                            <!-- Page C -->
                            <path d="M 50 160 Q 150 140 250 120 Q 350 115 450 115" stroke="#fbbc05" stroke-width="3" fill="none"/>
                            <circle cx="450" cy="115" r="4" fill="#fbbc05"/>
                            <text x="470" y="120" font-size="11" fill="#fbbc05" font-weight="bold">C: 0.343</text>

                            <!-- Page B -->
                            <path d="M 50 160 Q 150 170 250 160 Q 350 155 450 155" stroke="#34a853" stroke-width="3" fill="none"/>
                            <circle cx="450" cy="155" r="4" fill="#34a853"/>
                            <text x="470" y="160" font-size="11" fill="#34a853" font-weight="bold">B: 0.171</text>

                            <!-- Page D -->
                            <path d="M 50 160 Q 150 180 250 180 Q 350 175 450 175" stroke="#ea4335" stroke-width="3" fill="none"/>
                            <circle cx="450" cy="175" r="4" fill="#ea4335"/>
                            <text x="470" y="180" font-size="11" fill="#ea4335" font-weight="bold">D: 0.099</text>

                            <text x="250" y="20" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                                PageRank Convergence (d = 0.85)
                            </text>
                        </g>
                    </svg>
                </div>

                <div class="matrix">
                    <h4>Final PageRank Values</h4>
                    <table>
                        <tr><th>Page</th><th>PageRank</th><th>Rank</th><th>Interpretation</th></tr>
                        <tr><td style="background: #4285f4; color: white;"><strong>A</strong></td><td>0.387</td><td>1st</td><td>Most important (receives most links)</td></tr>
                        <tr><td style="background: #fbbc05; color: white;"><strong>C</strong></td><td>0.343</td><td>2nd</td><td>High importance</td></tr>
                        <tr><td style="background: #34a853; color: white;"><strong>B</strong></td><td>0.171</td><td>3rd</td><td>Medium importance</td></tr>
                        <tr><td style="background: #ea4335; color: white;"><strong>D</strong></td><td>0.099</td><td>4th</td><td>Lowest importance</td></tr>
                    </table>
                </div>
            </div>

            <div class="algorithm-step">
                <strong>📊 Key Observations:</strong><br>
                • Page A has the highest PageRank despite having no outlinks<br>
                • Page A receives links from B, C, and D<br>
                • Page D has the lowest PageRank despite linking to many pages<br>
                • The sum of all PageRank values equals 1.0
            </div>

            <h3>Iteration-by-Iteration Breakdown</h3>

            <button class="interactive-button" onclick="showDetailedCalculations()">Show Detailed Calculations →</button>

            <div id="detailed-calc" style="display: none;">
                <div class="algorithm-step">
                    <strong>Iteration 1 Calculations:</strong>

                    <div class="formula-box">
                        $$PR^{(1)}(A) = \frac{1-0.85}{4} + 0.85 \times \left(\frac{0.25}{2} + \frac{0.25}{1} + \frac{0.25}{3}\right)$$
                        $$= 0.0375 + 0.85 \times (0.125 + 0.25 + 0.083) = 0.427$$
                    </div>

                    <div class="formula-box">
                        $$PR^{(1)}(B) = \frac{1-0.85}{4} + 0.85 \times \frac{0.25}{3} = 0.0375 + 0.071 = 0.108$$
                    </div>

                    <div class="formula-box">
                        $$PR^{(1)}(C) = \frac{1-0.85}{4} + 0.85 \times \left(\frac{0.25}{2} + \frac{0.25}{3}\right) = 0.321$$
                    </div>

                    <div class="formula-box">
                        $$PR^{(1)}(D) = \frac{1-0.85}{4} + 0.85 \times 0 = 0.0375$$
                    </div>
                </div>

                <div class="highlight-box">
                    <strong>🎯 Convergence Analysis:</strong><br>
                    The algorithm typically converges in 50-100 iterations for real web graphs. For our 4-page example, convergence occurs around iteration 20 with the final values shown above.
                </div>
            </div>
        </section>

        <section id="implementation" class="section">
            <h2>💻 Implementation and Applications</h2>

            <h3>Python Implementation</h3>
            <p>Here's a complete implementation of the PageRank algorithm:</p>

            <div class="code-block">
import numpy as np

def pagerank(adjacency_matrix, damping_factor=0.85, max_iterations=100, tolerance=1e-6):
    """
    Compute PageRank using the power iteration method

    Args:
        adjacency_matrix: N×N matrix where entry (i,j) = 1 if page j links to page i
        damping_factor: probability of following links (default 0.85)
        max_iterations: maximum number of iterations
        tolerance: convergence threshold

    Returns:
        pagerank_vector: array of PageRank values for each page
    """
    N = adjacency_matrix.shape[0]

    # Handle dangling nodes (pages with no outlinks)
    out_degrees = np.sum(adjacency_matrix, axis=0)
    out_degrees[out_degrees == 0] = 1  # Avoid division by zero

    # Create transition matrix M
    M = adjacency_matrix / out_degrees

    # Handle dangling nodes by connecting them to all pages
    dangling_nodes = np.where(np.sum(adjacency_matrix, axis=0) == 0)[0]
    for node in dangling_nodes:
        M[:, node] = 1.0 / N

    # Initialize PageRank vector
    pagerank_vector = np.ones(N) / N

    # Power iteration
    for iteration in range(max_iterations):
        prev_pagerank = pagerank_vector.copy()

        # Apply PageRank formula: R = dMR + (1-d)/N * 1
        pagerank_vector = (damping_factor * M @ pagerank_vector +
                          (1 - damping_factor) / N * np.ones(N))

        # Check convergence
        if np.linalg.norm(pagerank_vector - prev_pagerank, 1) < tolerance:
            print(f"Converged after {iteration + 1} iterations")
            break

    return pagerank_vector

# Example usage
adjacency_matrix = np.array([
    [0, 1, 1, 1],  # A receives links from B, C, D
    [0, 0, 0, 1],  # B receives links from D
    [0, 1, 0, 1],  # C receives links from B, D
    [0, 0, 0, 0]   # D receives no links
])

pagerank_scores = pagerank(adjacency_matrix)
print("PageRank scores:", pagerank_scores)

# Sort pages by PageRank
pages = ['A', 'B', 'C', 'D']
ranked_pages = sorted(zip(pages, pagerank_scores), key=lambda x: x[1], reverse=True)
print("Ranking:", ranked_pages)
            </div>

            <h3>Real-World Applications</h3>

            <div class="algorithm-step">
                <strong>🔍 Web Search:</strong><br>
                • Google's original ranking algorithm<br>
                • Determines search result ordering<br>
                • Combined with content relevance signals
            </div>

            <div class="algorithm-step">
                <strong>📚 Academic Citations:</strong><br>
                • Rank scientific papers by citation networks<br>
                • Identify influential research<br>
                • Journal impact factor calculations
            </div>

            <div class="algorithm-step">
                <strong>🧬 Biological Networks:</strong><br>
                • Protein interaction networks<br>
                • Gene regulatory networks<br>
                • Metabolic pathway analysis
            </div>

            <div class="algorithm-step">
                <strong>🌐 Social Networks:</strong><br>
                • Identify influential users<br>
                • Recommendation systems<br>
                • Community detection
            </div>

            <div class="visualization">
                <h4>Performance Analysis</h4>
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Performance comparison -->
                    <g transform="translate(50, 50)">
                        <text x="250" y="0" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                            PageRank Performance Characteristics
                        </text>

                        <!-- Time complexity -->
                        <rect x="0" y="30" width="200" height="40" fill="#4285f4" rx="5"/>
                        <text x="100" y="55" font-size="12" fill="white" text-anchor="middle" font-weight="bold">
                            Time: O(k × N²)
                        </text>
                        <text x="100" y="80" font-size="10" fill="#2c3e50" text-anchor="middle">
                            k = iterations, N = pages
                        </text>

                        <!-- Space complexity -->
                        <rect x="220" y="30" width="200" height="40" fill="#34a853" rx="5"/>
                        <text x="320" y="55" font-size="12" fill="white" text-anchor="middle" font-weight="bold">
                            Space: O(N²)
                        </text>
                        <text x="320" y="80" font-size="10" fill="#2c3e50" text-anchor="middle">
                            Store transition matrix
                        </text>

                        <!-- Convergence -->
                        <rect x="0" y="100" width="200" height="40" fill="#fbbc05" rx="5"/>
                        <text x="100" y="125" font-size="12" fill="white" text-anchor="middle" font-weight="bold">
                            Convergence: ~50 iterations
                        </text>
                        <text x="100" y="150" font-size="10" fill="#2c3e50" text-anchor="middle">
                            For typical web graphs
                        </text>

                        <!-- Scalability -->
                        <rect x="220" y="100" width="200" height="40" fill="#ea4335" rx="5"/>
                        <text x="320" y="125" font-size="12" fill="white" text-anchor="middle" font-weight="bold">
                            Scalable to billions of pages
                        </text>
                        <text x="320" y="150" font-size="10" fill="#2c3e50" text-anchor="middle">
                            Google's original implementation
                        </text>

                        <!-- Modern optimizations -->
                        <text x="250" y="190" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">
                            Modern Optimizations
                        </text>

                        <text x="50" y="215" font-size="11" fill="#2c3e50">• Sparse matrix operations</text>
                        <text x="50" y="235" font-size="11" fill="#2c3e50">• Distributed computing</text>
                        <text x="250" y="215" font-size="11" fill="#2c3e50">• Block-based updates</text>
                        <text x="250" y="235" font-size="11" fill="#2c3e50">• Personalized PageRank</text>
                    </g>
                </svg>
            </div>

            <h3>Variations and Extensions</h3>

            <div class="algorithm-step">
                <strong>🎯 Personalized PageRank:</strong><br>
                Customize the random jump distribution to bias results toward specific topics or user preferences.
            </div>

            <div class="algorithm-step">
                <strong>⏰ Topic-Sensitive PageRank:</strong><br>
                Compute different PageRank vectors for different topics to improve search relevance.
            </div>

            <div class="algorithm-step">
                <strong>🔄 TrustRank:</strong><br>
                Start with trusted seed pages to combat web spam and link manipulation.
            </div>

            <div class="highlight-box">
                <strong>🎓 Conclusion</strong><br>
                PageRank revolutionized information retrieval by providing an objective, link-based measure of page importance. Its mathematical elegance (eigenvector centrality) combined with practical effectiveness made it the foundation of modern web search. While Google now uses hundreds of ranking factors, PageRank remains a fundamental component of search algorithms and has found applications far beyond web search.
            </div>

            <h3>Try It Yourself!</h3>
            <div class="code-block">
# NetworkX implementation (easiest to use)
import networkx as nx

# Create a directed graph
G = nx.DiGraph()
G.add_edges_from([('B', 'A'), ('B', 'C'), ('C', 'A'), ('D', 'A'), ('D', 'B'), ('D', 'C')])

# Compute PageRank
pagerank_scores = nx.pagerank(G, alpha=0.85)
print("PageRank scores:", pagerank_scores)

# Visualize the graph
import matplotlib.pyplot as plt
pos = nx.spring_layout(G)
nx.draw(G, pos, with_labels=True, node_color='lightblue',
        node_size=[v * 3000 for v in pagerank_scores.values()])
plt.show()
            </div>
        </section>
    </div>

    <script>
        // Add smooth scrolling for navigation
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Interactive example functions
        function showIterations() {
            document.getElementById('iterations').style.display = 'block';
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([document.getElementById('iterations')]).catch((err) => console.log(err.message));
            }
        }

        function showConvergence() {
            document.getElementById('convergence').style.display = 'block';
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([document.getElementById('convergence')]).catch((err) => console.log(err.message));
            }
        }

        function showDetailedCalculations() {
            document.getElementById('detailed-calc').style.display = 'block';
            event.target.style.display = 'none';

            // Re-render MathJax for new content
            if (window.MathJax) {
                MathJax.typesetPromise([document.getElementById('detailed-calc')]).catch((err) => console.log(err.message));
            }
        }

        // Add interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to page nodes
            const pageNodes = document.querySelectorAll('.page-node');
            pageNodes.forEach(node => {
                node.addEventListener('mouseenter', function() {
                    this.style.filter = 'brightness(1.2)';
                    this.style.cursor = 'pointer';
                });

                node.addEventListener('mouseleave', function() {
                    this.style.filter = 'brightness(1)';
                });

                node.addEventListener('click', function() {
                    // Show page details
                    const pageId = this.querySelector('text').textContent;
                    showPageDetails(pageId);
                });
            });

            // Add hover effects to table cells
            const tableCells = document.querySelectorAll('td');
            tableCells.forEach(cell => {
                cell.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.2s';
                    this.style.backgroundColor = '#e3f2fd';
                });

                cell.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.backgroundColor = '';
                });
            });

            // Add progress indicator for navigation
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');

            window.addEventListener('scroll', function() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });

            // Add animation to step counters
            const stepCounters = document.querySelectorAll('.step-counter');
            stepCounters.forEach(counter => {
                counter.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(360deg)';
                    this.style.transition = 'transform 0.5s';
                });

                counter.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });

            // Add interactive code block functionality
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.addEventListener('click', function() {
                    // Select all text in the code block
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Show notification
                    showNotification('Code copied to clipboard!');
                });
            });
        });

        function showPageDetails(pageId) {
            const details = {
                'A': 'Page A: Highest PageRank (0.387) - Receives links from B, C, and D',
                'B': 'Page B: Medium PageRank (0.171) - Links to A and C, receives from D',
                'C': 'Page C: High PageRank (0.343) - Links to A, receives from B and D',
                'D': 'Page D: Lowest PageRank (0.099) - Links to A, B, C but receives none'
            };

            showNotification(details[pageId] || 'Page details not available');
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #4285f4;
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
                max-width: 80%;
                text-align: center;
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS for active navigation and enhanced interactivity
        const style = document.createElement('style');
        style.textContent = `
            .navigation a.active {
                color: #4285f4 !important;
                font-weight: bold;
            }

            .navigation a.active::before {
                content: "→ ";
                color: #4285f4;
            }

            .algorithm-step:hover {
                transform: translateX(5px);
                transition: transform 0.3s ease;
            }

            .formula-box:hover {
                box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block:hover {
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: box-shadow 0.3s ease;
            }

            .code-block::after {
                content: "Click to select all";
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.1);
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .code-block:hover::after {
                opacity: 1;
            }

            .code-block {
                position: relative;
            }

            .google-colors {
                animation: rainbow 3s ease-in-out infinite;
            }

            @keyframes rainbow {
                0%, 100% { filter: hue-rotate(0deg); }
                50% { filter: hue-rotate(180deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
